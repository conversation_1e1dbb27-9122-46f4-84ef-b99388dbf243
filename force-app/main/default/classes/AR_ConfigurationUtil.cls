/**
 * Utility class for managing Assistance Request configuration settings
 * Provides centralized access to configuration values stored in custom metadata
 */
public with sharing class AR_ConfigurationUtil {
    
    // Configuration keys
    public static final String ORG_WIDE_EMAIL_KEY = 'Org_Wide_Email_Address_Id';
    public static final String P1_SPECIAL_QUEUE_KEY = 'P1_Special_Priority_Queue_Name';
    public static final String UNASSIGNED_QUEUE_KEY = 'Unassigned_Queue_Name';
    public static final String REGIONAL_MANAGER_TITLE_KEY = 'Regional_Manager_Title';

    // Email template configuration keys
    public static final String P1_INITIAL_NOTIFICATION_TEMPLATE_KEY = 'P1_Initial_Notification_Template';
    public static final String P1_REMINDER_15MIN_TEMPLATE_KEY = 'P1_Reminder_15Min_Template';
    public static final String P1_REMINDER_20MIN_TEMPLATE_KEY = 'P1_Reminder_20Min_Template';
    public static final String P1_REMINDER_30MIN_TEMPLATE_KEY = 'P1_Reminder_30Min_Template';
    public static final String P1_ESCALATION_TEMPLATE_KEY = 'P1_Escalation_Template';

    // SLA timing configuration keys
    public static final String P1_FIRST_REMINDER_MINUTES_KEY = 'P1_First_Reminder_Minutes';
    public static final String P1_SECOND_REMINDER_MINUTES_KEY = 'P1_Second_Reminder_Minutes';
    public static final String P1_THIRD_REMINDER_MINUTES_KEY = 'P1_Third_Reminder_Minutes';
    public static final String P1_MAX_REMINDERS_KEY = 'P1_Max_Reminders';

    // Default values
    private static final String DEFAULT_ORG_WIDE_EMAIL = '0D2xx000000DOWq';
    private static final String DEFAULT_P1_QUEUE = 'AR_Special_Priority_Queue';
    private static final String DEFAULT_UNASSIGNED_QUEUE = 'UnAssigned_AR';
    private static final String DEFAULT_REGIONAL_MANAGER_TITLE = 'Regional Manager';

    // Default email template names
    private static final String DEFAULT_P1_INITIAL_TEMPLATE = 'P1_AR_Initial_Assignment';
    private static final String DEFAULT_P1_REMINDER_15_TEMPLATE = 'P1_AR_Reminder_15Min';
    private static final String DEFAULT_P1_REMINDER_20_TEMPLATE = 'P1_AR_Reminder_20Min';
    private static final String DEFAULT_P1_REMINDER_30_TEMPLATE = 'P1_AR_Reminder_30Min';
    private static final String DEFAULT_P1_ESCALATION_TEMPLATE = 'P1_AR_Escalation';

    // Default SLA timing values (in minutes)
    private static final Integer DEFAULT_FIRST_REMINDER_MINUTES = 15;
    private static final Integer DEFAULT_SECOND_REMINDER_MINUTES = 20;
    private static final Integer DEFAULT_THIRD_REMINDER_MINUTES = 30;
    private static final Integer DEFAULT_MAX_REMINDERS = 3;
    
    // Cache for configuration values
    private static Map<String, String> configCache;
    
    /**
     * Gets the org-wide email address ID for SLA notifications
     * @return Org-wide email address ID
     */
    public static String getOrgWideEmailAddressId() {
        return getConfigValue(ORG_WIDE_EMAIL_KEY, DEFAULT_ORG_WIDE_EMAIL);
    }
    
    /**
     * Gets the P1 escalation queue name
     * @return P1 escalation queue developer name
     */
    public static String getP1EscalationQueueName() {
        return getConfigValue(P1_ESCALATION_QUEUE_KEY, DEFAULT_P1_QUEUE);
    }
    
    /**
     * Gets the unassigned queue name
     * @return Unassigned queue developer name
     */
    public static String getUnassignedQueueName() {
        return getConfigValue(UNASSIGNED_QUEUE_KEY, DEFAULT_UNASSIGNED_QUEUE);
    }
    
    /**
     * Gets the regional manager title for P1 escalation
     * @return Regional manager title
     */
    public static String getRegionalManagerTitle() {
        return getConfigValue(REGIONAL_MANAGER_TITLE_KEY, DEFAULT_REGIONAL_MANAGER_TITLE);
    }
    
    /**
     * Gets a configuration value by key
     * @param key Configuration key
     * @param defaultValue Default value if configuration not found
     * @return Configuration value or default
     */
    public static String getConfigValue(String key, String defaultValue) {
        if (String.isBlank(key)) {
            return defaultValue;
        }
        
        try {
            // Initialize cache if needed
            if (configCache == null) {
                loadConfigurationCache();
            }
            
            String value = configCache.get(key);
            return String.isNotBlank(value) ? value : defaultValue;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to get config value for key ' + key + ': ' + e.getMessage());
            return defaultValue;
        }
    }
    
    /**
     * Loads all configuration values into cache
     */
    private static void loadConfigurationCache() {
        configCache = new Map<String, String>();
        
        try {
            List<AR_Configuration__mdt> configs = [
                SELECT DeveloperName, Value__c, Is_Active__c
                FROM AR_Configuration__mdt
                WHERE Is_Active__c = true
            ];
            
            for (AR_Configuration__mdt config : configs) {
                if (String.isNotBlank(config.Value__c)) {
                    configCache.put(config.DeveloperName, config.Value__c);
                }
            }
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Failed to load configuration cache: ' + e.getMessage());
            // Initialize empty cache to prevent null pointer exceptions
            configCache = new Map<String, String>();
        }
    }
    
    /**
     * Refreshes the configuration cache
     * Call this method if configuration values are updated
     */
    public static void refreshCache() {
        configCache = null;
        loadConfigurationCache();
    }
    
    /**
     * Gets all active configuration values
     * @return Map of configuration key-value pairs
     */
    public static Map<String, String> getAllConfigurations() {
        if (configCache == null) {
            loadConfigurationCache();
        }
        return configCache.clone();
    }
    
    /**
     * Validates that required configurations are present
     * @return List of missing configuration keys
     */
    public static List<String> validateRequiredConfigurations() {
        List<String> missingConfigs = new List<String>();
        List<String> requiredKeys = new List<String>{
            ORG_WIDE_EMAIL_KEY,
            P1_ESCALATION_QUEUE_KEY,
            UNASSIGNED_QUEUE_KEY,
            REGIONAL_MANAGER_TITLE_KEY
        };
        
        if (configCache == null) {
            loadConfigurationCache();
        }
        
        for (String key : requiredKeys) {
            if (!configCache.containsKey(key) || String.isBlank(configCache.get(key))) {
                missingConfigs.add(key);
            }
        }
        
        return missingConfigs;
    }
    
    /**
     * Exception class for configuration-related errors
     */
    public class ConfigurationException extends Exception {}
}
