/**
 * Created by <PERSON> on 24-06-2025.
 */

/* STRY0567712 -- As an AR Queue Manager I require the system to send an Alert if a P1 AR has not been assigned to a person within 15 minutes **/
public with sharing class AR_SLA_EmailFactory {

	private static final String DEFAULT_ORG_WIDE_EMAIL = '0D2xx000000DOWq';

	/**
	 * Creates a reminder email for an Assistance Request
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage reminder(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		return build(ar, 'AR_Reminder_' + ar.Reminder_Count__c);
	}

	/**
	 * Creates a manager escalation email for an Assistance Request
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage managerEscalation(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		return build(ar, 'AR_Escalation_To_Manager');
	}

	/**
	 * Creates a P1 alert email for an Assistance Request
	 * @param ar The Assistance Request record
	 * @param min The minute threshold for the alert
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1Alert(Assistance_Request__c ar, Integer min) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		if (min == null || min <= 0) {
			throw new IllegalArgumentException('Minute threshold must be positive');
		}
		return build(ar, 'AR_Pickup_Alert_' + min);
	}

	/**
	 * Creates initial P1 assignment notification email for queue members
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1InitialNotification(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		String templateName = AR_ConfigurationUtil.getP1InitialNotificationTemplate();
		return build(ar, templateName);
	}

	/**
	 * Creates P1 15-minute reminder email
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1Reminder15Min(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		String templateName = AR_ConfigurationUtil.getP1Reminder15MinTemplate();
		return build(ar, templateName);
	}

	/**
	 * Creates P1 20-minute reminder email
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1Reminder20Min(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		String templateName = AR_ConfigurationUtil.getP1Reminder20MinTemplate();
		return build(ar, templateName);
	}

	/**
	 * Creates P1 30-minute reminder email
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1Reminder30Min(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		String templateName = AR_ConfigurationUtil.getP1Reminder30MinTemplate();
		return build(ar, templateName);
	}

	/**
	 * Creates P1 escalation email for queue owner
	 * @param ar The Assistance Request record
	 * @return Configured email message
	 */
	public static Messaging.SingleEmailMessage p1Escalation(Assistance_Request__c ar) {
		if (ar == null) {
			throw new IllegalArgumentException('Assistance Request cannot be null');
		}
		String templateName = AR_ConfigurationUtil.getP1EscalationTemplate();
		return build(ar, templateName);
	}

	/**
	 * Builds an email message with the specified template
	 * @param ar The Assistance Request record
	 * @param templateDevName The developer name of the email template
	 * @return Configured email message
	 */
	private static Messaging.SingleEmailMessage build(Assistance_Request__c ar, String templateDevName) {
		try {
			Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
			
			// Get org-wide email address from custom metadata or use default
			String orgWideEmailId = getOrgWideEmailAddress();
			if (String.isNotBlank(orgWideEmailId)) {
				mail.setOrgWideEmailAddressId(orgWideEmailId);
			}
			
			mail.setTargetObjectId(ar.OwnerId);
			mail.setWhatId(ar.Id);
			
			// Get email template
			EmailTemplate template = getEmailTemplate(templateDevName);
			if (template != null) {
				mail.setTemplateId(template.Id);
			} else {
				throw new EmailFactoryException('Email template not found: ' + templateDevName);
			}
			
			mail.setSaveAsActivity(false);
			return mail;
			
		} catch (Exception e) {
			throw new EmailFactoryException('Failed to build email: ' + e.getMessage(), e);
		}
	}

	/**
	 * Retrieves the org-wide email address from custom metadata
	 * @return Org-wide email address ID
	 */
	private static String getOrgWideEmailAddress() {
		try {
			return AR_ConfigurationUtil.getOrgWideEmailAddressId();
		} catch (Exception e) {
			System.debug('Failed to retrieve org-wide email address: ' + e.getMessage());
			return DEFAULT_ORG_WIDE_EMAIL;
		}
	}

	/**
	 * Retrieves email template by developer name
	 * @param templateDevName The developer name of the template
	 * @return EmailTemplate record or null if not found
	 */
	private static EmailTemplate getEmailTemplate(String templateDevName) {
		try {
			List<EmailTemplate> templates = [
				SELECT Id 
				FROM EmailTemplate 
				WHERE DeveloperName = :templateDevName 
				LIMIT 1
			];
			return templates.isEmpty() ? null : templates[0];
		} catch (Exception e) {
			System.debug('Failed to retrieve email template: ' + e.getMessage());
			return null;
		}
	}

	/**
	 * Custom exception for email factory errors
	 */
	public class EmailFactoryException extends Exception {}
}
