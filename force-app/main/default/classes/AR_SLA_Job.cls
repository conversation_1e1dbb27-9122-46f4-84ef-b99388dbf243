/**
 * Created by <PERSON> on 24-06-2025.
 */

/* STRY0567712 -- As an AR Queue Manager I require the system to send an Alert if a P1 AR has not been assigned to a person within 15 minutes **/

public with sharing class AR_SLA_Job implements Schedulable {

	/**
	 * Main execution method for the scheduled job
	 * @param sc SchedulableContext
	 */
	public void execute(SchedulableContext sc) {
		try {
			processAssistanceRequests();
		} catch (Exception e) {
			System.debug(LoggingLevel.ERROR, 'AR_SLA_Job execution failed: ' + e.getMessage());
			// TODO: Add error notification mechanism
		}
	}

	/**
	 * Processes all open Assistance Requests for SLA monitoring
	 */
	private void processAssistanceRequests() {
		Datetime now = System.now();

		// Query all open Assistance Requests
		List<Assistance_Request__c> ars = queryOpenAssistanceRequests();
		
		if (ars.isEmpty()) {
			return;
		}

		// Get SLA configuration
		Map<String, AR_SLA_Definition__mdt> slaConfig = getSLAConfiguration();

		List<Assistance_Request__c> updates = new List<Assistance_Request__c>();
		List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();

		// Process each Assistance Request
		for (Assistance_Request__c ar : ars) {
			try {
				processIndividualAR(ar, now, slaConfig, updates, emails);
			} catch (Exception e) {
				System.debug(LoggingLevel.ERROR, 'Failed to process AR ' + ar.Id + ': ' + e.getMessage());
				continue; // Continue processing other records
			}
		}

		// Perform DML operations
		performUpdates(updates);
		sendEmails(emails);
	}

	/**
	 * Queries all open Assistance Requests
	 * @return List of Assistance Request records
	 */
	private List<Assistance_Request__c> queryOpenAssistanceRequests() {
		return [
			SELECT Id, Priority__c, Status__c, OwnerId,
				   Reminder_Count__c, P1_Alert_History__c,
				   CreatedDate, SLA_Breached__c
			FROM Assistance_Request__c
			WHERE Status__c != 'Closed'
			ORDER BY Priority__c, CreatedDate
		];
	}

	/**
	 * Retrieves SLA configuration from custom metadata
	 * @return Map of SLA configurations by priority
	 */
	private Map<String, AR_SLA_Definition__mdt> getSLAConfiguration() {
		try {
			return AR_SLA_Definition__mdt.getAll();
		} catch (Exception e) {
			System.debug(LoggingLevel.ERROR, 'Failed to retrieve SLA configuration: ' + e.getMessage());
			return new Map<String, AR_SLA_Definition__mdt>();
		}
	}

	/**
	 * Processes an individual Assistance Request
	 * @param ar The Assistance Request to process
	 * @param now Current datetime
	 * @param slaConfig SLA configuration map
	 * @param updates List to collect update records
	 * @param emails List to collect email messages
	 */
	private void processIndividualAR(
		Assistance_Request__c ar, 
		Datetime now, 
		Map<String, AR_SLA_Definition__mdt> slaConfig,
		List<Assistance_Request__c> updates, 
		List<Messaging.SingleEmailMessage> emails
	) {
		AR_SLA_Definition__mdt slaRule = slaConfig.get(ar.Priority__c);
		if (slaRule == null) {
			return;
		}

		Integer ageMinutes = calculateAgeInMinutes(ar.CreatedDate, now);

		// Handle P1 special processing
		if (ar.Priority__c == 'P1') {
			P1SpecialProcessor processor = new P1SpecialProcessor();
			emails.addAll(processor.processP1Request(ar, ageMinutes, updates));
			return; // P1 requests have their own processing logic
		}

		// Handle work-start reminder loop
		if (ar.Status__c != 'In Progress') {
			processWorkStartReminders(ar, ageMinutes, slaRule, updates, emails);
		}
	}

	/**
	 * Calculates age in minutes between two datetimes
	 * @param createdDate The creation datetime
	 * @param currentTime The current datetime
	 * @return Age in minutes
	 */
	private Integer calculateAgeInMinutes(Datetime createdDate, Datetime currentTime) {
		if (createdDate == null || currentTime == null) {
			return 0;
		}
		return Integer.valueOf((currentTime.getTime() - createdDate.getTime()) / 60000);
	}

	/**
	 * Processes work-start reminders for non-P1 or assigned requests
	 * @param ar The Assistance Request
	 * @param ageMinutes Age in minutes
	 * @param slaRule SLA configuration rule
	 * @param updates List to collect updates
	 * @param emails List to collect emails
	 */
	private void processWorkStartReminders(
		Assistance_Request__c ar,
		Integer ageMinutes,
		AR_SLA_Definition__mdt slaRule,
		List<Assistance_Request__c> updates,
		List<Messaging.SingleEmailMessage> emails
	) {
		if (slaRule.WorkStart_Min__c == null || slaRule.Max_Reminders__c == null) {
			return;
		}

		Integer reminderCount = ar.Reminder_Count__c != null ? Integer.valueOf(ar.Reminder_Count__c) : 0;
		Integer nextDueMinutes = (reminderCount + 1) * Integer.valueOf(slaRule.WorkStart_Min__c);

		if (ageMinutes >= nextDueMinutes && reminderCount < slaRule.Max_Reminders__c) {
			// Send reminder
			sendReminder(ar, reminderCount + 1, updates, emails);
		} else if (ageMinutes >= nextDueMinutes && 
				   reminderCount >= slaRule.Max_Reminders__c && 
				   !ar.SLA_Breached__c) {
			// Escalate to manager
			escalateToManager(ar, updates, emails);
		}
	}

	/**
	 * Sends a reminder email and updates the reminder count
	 * @param ar The Assistance Request
	 * @param newReminderCount The new reminder count
	 * @param updates List to collect updates
	 * @param emails List to collect emails
	 */
	private void sendReminder(
		Assistance_Request__c ar,
		Integer newReminderCount,
		List<Assistance_Request__c> updates,
		List<Messaging.SingleEmailMessage> emails
	) {
		ar.Reminder_Count__c = newReminderCount;
		updates.add(new Assistance_Request__c(
			Id = ar.Id,
			Reminder_Count__c = newReminderCount
		));
		emails.add(AR_SLA_EmailFactory.reminder(ar));
	}

	/**
	 * Escalates the request to manager when SLA is breached
	 * @param ar The Assistance Request
	 * @param updates List to collect updates
	 * @param emails List to collect emails
	 */
	private void escalateToManager(
		Assistance_Request__c ar,
		List<Assistance_Request__c> updates,
		List<Messaging.SingleEmailMessage> emails
	) {
		Id managerId = getManagerId(ar);
		if (managerId != null) {
			ar.SLA_Breached__c = true;
			ar.OwnerId = managerId;
			updates.add(new Assistance_Request__c(
				Id = ar.Id,
				OwnerId = managerId,
				SLA_Breached__c = true
			));
			emails.add(AR_SLA_EmailFactory.managerEscalation(ar));
		}
	}

	/**
	 * Gets the manager ID for escalation
	 * @param ar The Assistance Request
	 * @return Manager's user ID
	 */
	private Id getManagerId(Assistance_Request__c ar) {
		try {
			if (ar.OwnerId == null) {
				return null;
			}
			
			List<User> users = [
				SELECT ManagerId 
				FROM User 
				WHERE Id = :ar.OwnerId 
				LIMIT 1
			];
			
			return users.isEmpty() ? null : users[0].ManagerId;
		} catch (Exception e) {
			System.debug(LoggingLevel.ERROR, 'Failed to get manager ID: ' + e.getMessage());
			return null;
		}
	}

	/**
	 * Performs bulk updates on Assistance Request records
	 * @param updates List of records to update
	 */
	private void performUpdates(List<Assistance_Request__c> updates) {
		if (!updates.isEmpty()) {
			try {
				update updates;
			} catch (Exception e) {
				System.debug(LoggingLevel.ERROR, 'Failed to update Assistance Requests: ' + e.getMessage());
			}
		}
	}

	/**
	 * Sends bulk emails
	 * @param emails List of email messages to send
	 */
	private void sendEmails(List<Messaging.SingleEmailMessage> emails) {
		if (!emails.isEmpty()) {
			try {
				Messaging.sendEmail(emails);
			} catch (Exception e) {
				System.debug(LoggingLevel.ERROR, 'Failed to send emails: ' + e.getMessage());
			}
		}
	}

	/**
	 * Inner class to handle P1 alert processing
	 */
	private class P1AlertProcessor {
		private static final Set<Integer> P1_ALERT_THRESHOLDS = new Set<Integer>{10, 20, 30};

		/**
		 * Processes P1 alerts for unassigned high-priority requests
		 * @param ar The Assistance Request
		 * @param ageMinutes Age in minutes
		 * @param updates List to collect updates
		 * @return List of email messages to send
		 */
		public List<Messaging.SingleEmailMessage> processP1Alerts(
			Assistance_Request__c ar,
			Integer ageMinutes,
			List<Assistance_Request__c> updates
		) {
			List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();

			Set<Integer> alreadyAlerted = parseAlertHistory(ar.P1_Alert_History__c);
			Set<Integer> newAlerts = new Set<Integer>();

			// Check each threshold
			for (Integer threshold : P1_ALERT_THRESHOLDS) {
				if (ageMinutes >= threshold && !alreadyAlerted.contains(threshold)) {
					emails.add(AR_SLA_EmailFactory.p1Alert(ar, threshold));
					newAlerts.add(threshold);
					alreadyAlerted.add(threshold);

					// Hard escalate at 30 minutes
					if (threshold == 30) {
						escalateP1ToRegionalManager(ar, updates);
					}
				}
			}

			// Update alert history if new alerts were sent
			if (!newAlerts.isEmpty()) {
				updateP1AlertHistory(ar, alreadyAlerted, updates);
			}

			return emails;
		}

		/**
		 * Parses the P1 alert history string into a set of integers
		 * @param historyString The alert history string
		 * @return Set of alert thresholds already processed
		 */
		private Set<Integer> parseAlertHistory(String historyString) {
			Set<Integer> history = new Set<Integer>();
			if (String.isNotBlank(historyString)) {
				for (String part : historyString.split('/')) {
					try {
						history.add(Integer.valueOf(part.trim()));
					} catch (Exception e) {
						System.debug('Invalid alert history value: ' + part);
					}
				}
			}
			return history;
		}

		/**
		 * Updates the P1 alert history field
		 * @param ar The Assistance Request
		 * @param alertHistory Set of all alerts sent
		 * @param updates List to collect updates
		 */
		private void updateP1AlertHistory(
			Assistance_Request__c ar,
			Set<Integer> alertHistory,
			List<Assistance_Request__c> updates
		) {
			List<Integer> sortedHistory = new List<Integer>(alertHistory);
			sortedHistory.sort();

			updates.add(new Assistance_Request__c(
				Id = ar.Id,
				P1_Alert_History__c = String.join(sortedHistory, '/')
			));
		}

		/**
		 * Escalates P1 request to regional manager at 30-minute threshold
		 * @param ar The Assistance Request
		 * @param updates List to collect updates
		 */
		private void escalateP1ToRegionalManager(
			Assistance_Request__c ar,
			List<Assistance_Request__c> updates
		) {
			Id regionalManagerId = getRegionalManagerId();
			if (regionalManagerId != null) {
				updates.add(new Assistance_Request__c(
					Id = ar.Id,
					OwnerId = regionalManagerId
				));
			}
		}

		/**
		 * Gets the regional manager ID for P1 escalation
		 * @return Regional manager's user ID
		 */
		private Id getRegionalManagerId() {
			try {
				String managerTitle = AR_ConfigurationUtil.getRegionalManagerTitle();
				List<User> managers = [
					SELECT Id
					FROM User
					WHERE IsActive = true
					AND Title = :managerTitle
					LIMIT 1
				];
				return managers.isEmpty() ? null : managers[0].Id;
			} catch (Exception e) {
				System.debug(LoggingLevel.ERROR, 'Failed to get regional manager: ' + e.getMessage());
				return null;
			}
		}
	}
}
