/**
 * Trigger for Assistance Request object
 * Handles before and after insert/update operations
 *
 * STRY0163270 - Provide ability to track "Days between updates"
 * STRY0567712 - AR Queue Manager SLA alerts for P1 requests
 */
trigger AssistanceRequestOnUpdate on Assistance_Request__c (before insert, before update, after insert, after update) {

    // Initialize trigger handler
    AssistanceReqTrigHandler handler = new AssistanceReqTrigHandler();

    try {
        // Before trigger operations
        if (Trigger.isBefore) {
            if (Trigger.isInsert) {
                handler.routeOwners(Trigger.new);
            }

            if (Trigger.isUpdate) {
                handler.countAverageDaysBetweenUpdate(Trigger.new, Trigger.oldMap);
                handler.routeOwners(Trigger.new);
                handler.handleP1PriorityChanges(Trigger.new, Trigger.oldMap);
            }
        }

        // After trigger operations
        if (Trigger.isAfter) {
            if (Trigger.isUpdate) {
                handler.resetReminderCounter(Trigger.old, Trigger.new);
            }

            // Future enhancement: Add chatter follow functionality
            // if (Trigger.isInsert || Trigger.isUpdate) {
            //     handler.followAROwner(Trigger.oldMap, Trigger.newMap);
            // }
        }

    } catch (Exception e) {
        // Log error and add to trigger context
        System.debug(LoggingLevel.ERROR, 'AssistanceRequestOnUpdate trigger error: ' + e.getMessage());

        // Add error to the first record to prevent data corruption
        if (!Trigger.new.isEmpty()) {
            Trigger.new[0].addError('An error occurred while processing the Assistance Request: ' + e.getMessage());
        }
    }
}