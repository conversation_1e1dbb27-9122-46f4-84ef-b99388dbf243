trigger AssistanceRequestOnUpdate on Assistance_Request__c (after insert, after update,before update) {

    AssistanceReqTrigHandler handler = new AssistanceReqTrigHandler();
    if (Trigger.IsAfter) {
        if (Trigger.IsInsert || Trigger.IsUpdate){
            //handler.followAROwner(trigger.new);
            //handler.followAROwner(trigger.oldMap, trigger.newMap);
        }
    }
        /*
    <!-- release story no : STRY0163270 description START :Provide ability to track "Days between updates"  -->*/
    if (Trigger.IsBefore) {
        if (Trigger.IsUpdate){
            handler.countAverageDaysBetweenUpdate(trigger.new,trigger.oldMap);
        }
    }
    /* your code <!-- release story no :STRY0163270 description END-->*/
}