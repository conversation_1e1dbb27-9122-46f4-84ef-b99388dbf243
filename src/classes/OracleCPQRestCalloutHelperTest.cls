@isTest
public class OracleCPQRestCalloutHelperTest {
    
    @isTest
    static void testGetUserPerformanceLogs() {
        Test.setMock(HttpCalloutMock.class, new OracleCPQCalloutMock());
        
        List<OracleCPQRestCalloutHelper.InputVariable> inputList = new List<OracleCPQRestCalloutHelper.InputVariable>();
        OracleCPQRestCalloutHelper.InputVariable input=new OracleCPQRestCalloutHelper.InputVariable();
        input.numberOfDays=7;
        inputList.add(input);
        
        // Call the getUserPerformanceLogs method
        //OracleCPQRestCalloutHelper.getUserPerformanceLogs();
        Test.startTest();
        OracleCPQRestCalloutHelper.invokeOracleCPQRestAPI(inputList);
        
        Test.stopTest();
        
        
    }
    
    private class OracleCPQCalloutMock implements HttpCalloutMock {
        // Implement this interface method
        public HTTPResponse respond(HTTPRequest request) {
            // Create a fake response
            HttpResponse response = new HttpResponse();
            response.setHeader('Content-Type', 'application/json');
            response.setBody('{"hasMore":true,"links":[{"rel":"self","href":"https://qavarian.bigmachines.com/rest/v14/performanceLogs"}],"items":[{"applicationVersion":"23.4.5","referenceObject":"general","ipAddress":null,"sessionId":null,"login":"superuser","transactionId":null,"url":null,"node":"node1","component":"urldatabypost","createdDate":"2024-01-19","eventData":null,"requestId":null,"browserVersion":null,"modifiedDate":"2024-01-19","browserTime":null,"serverTime":60,"id":226740394,"event":"BML","eventDate":"2024-01-19T09:35:02.000Z","links":[{"rel":"self","href":"https://qavarian.bigmachines.com/rest/v14/performanceLogs/226740394"}]},{"applicationVersion":"23.4.5","referenceObject":"quickstart_commerce_process.quote_process","ipAddress":null,"sessionId":null,"login":"superuser","transactionId":10202441819,"url":null,"node":"node1","component":"Other Commerce Rules","createdDate":"2024-01-19","eventData":null,"requestId":null,"browserVersion":null,"modifiedDate":"2024-01-19","browserTime":null,"serverTime":3,"id":226740404,"event":"Commerce Rule","eventDate":"2024-01-19T09:35:02.000Z","links":[{"rel":"self","href":"https://qavarian.bigmachines.com/rest/v14/performanceLogs/226740404"}]}]}');
            response.setStatusCode(200);
            return response; 
        }
    }
}