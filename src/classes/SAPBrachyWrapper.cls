/**
 *  Class Name: SAPBrachyWrapper  
 *  Description: This is a Wrapper used for sending the response Back to BOOMI after processing the request. 
 *  Company: Varian
 *  CreatedDate: May 01, 2020
 *  
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON><PERSON><PERSON><PERSON><PERSON>         May 22, 2020            Original version
 *            
 */
global class SAPBrachyWrapper 
{
    
    global string overallresponse;
    global string overallmessage;
    global List<SalesOrderWrapper> wrapperlist;
    
    global SAPBrachyWrapper(String o,String e,List<SalesOrderWrapper> w)
    {
     this.overallresponse = o;
     this.wrapperlist = w;   
     this.overallmessage = e;
    }
    
    global void setval(String key,String value)
    {
        boolean found = false;
        for(SalesOrderWrapper so : this.wrapperlist)
        {
            if(so.key.equals(key)) 
            {
                so.value = so.value + value;
                found = true;
            }
        }
        if(!found) 
        {
            this.wrapperlist.add(new SalesOrderWrapper(key,value));
        }
    }

}