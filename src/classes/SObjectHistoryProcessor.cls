/***********************************************************************
* Author         : Varian
* Description    : A handler class which compare fields changed for sobjects during trigger execution for field tracking
* User Story     : 
* Created On     : 5/2/16
* Changed On     : 11/8/19 - updated api for FSL project
************************************************************************/

public without sharing class SObjectHistoryProcessor implements Queueable {
    //Events to tap - after insert, after update, before delete, before undelete
    private static final String IS_INSERT = 'insert', IS_UPDATE = 'update', IS_DELETE = 'delete', IS_BEFORE = 'before', IS_AFTER = 'after', IS_UNDELETE = 'undelete', NEW_LIST = 'new',
                                                NEW_MAP = 'newmap', OLD_MAP = 'oldmap', CREATED = 'Created';
    public static boolean TRUN_OFF_TRACKING = false;
    public Map<String, boolean> events;
    public Map<String, String> objData;
    public String objAPIName;
    public Datetime currentTimeStamp;
    public Id ownerId;
    public static Id mirCaseRecordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByDeveloperName().get('MIR').getRecordTypeId();
    public static Set<Id> userIdSet;
    public static Set<Id> contactIdSet;
    public static Map<String, User> userMap;
    public static Map<String, Group> groupMap;
    public static Map<String, Contact> contactMap;
    
    public SObjectHistoryProcessor (Map<String, boolean> evts, Map<String, String> odata, String apiName, Datetime timeStamp, Id oId) {
        this.events = evts;
        this.objData = odata;
        this.objAPIName = apiName;
        this.currentTimeStamp = timeStamp;
        this.ownerId = oId; 
    }
    
    public void execute(QueueableContext SC) {
        SObjectHistoryProcessor.processHistory(this.events, this.objData, this.objAPIName, this.currentTimeStamp, this.ownerId);    
    }
    
    public static void trackHistory(String objAPIName) {
        /*
        Datetime currentTime = System.Now();
        Map<String, boolean> eventsMap = !Test.isRunningTest() ?  new Map<String, boolean> {SObjectHistoryProcessor.IS_INSERT => trigger.isInsert, SObjectHistoryProcessor.IS_UPDATE => trigger.isUpdate, 
                                            SObjectHistoryProcessor.IS_DELETE => trigger.isDelete, SObjectHistoryProcessor.IS_BEFORE => trigger.isBefore, 
                                            SObjectHistoryProcessor.IS_AFTER => trigger.isAfter, SObjectHistoryProcessor.IS_UNDELETE => trigger.isUndelete} : new Map<String, boolean>();
        Map<String, String> dataMap = !Test.isRunningTest() ? new Map<String, String> { SObjectHistoryProcessor.NEW_MAP => JSON.serialize(trigger.newMap), 
                                        SObjectHistoryProcessor.OLD_MAP => JSON.serialize(trigger.oldMap), SObjectHistoryProcessor.NEW_LIST => trigger.isUndelete || trigger.isDelete ? JSON.serialize(trigger.new) : ''} 
                                        : new Map<String, String>();
        if (!SObjectHistoryProcessor.TRUN_OFF_TRACKING) {
            if (Limits.getQueueableJobs() < Limits.getLimitQueueableJobs()) {
                Id jobId = System.enqueueJob(new SObjectHistoryProcessor(eventsMap, dataMap, objAPIName, currentTime, Userinfo.getUserId()));
            }
        }  */     
    }
    
    public static void processHistory(Map<String, boolean> events, Map<String, String> objData, String objAPIName, Datetime currentTimeStamp, Id ownerId) {
        if (events !=null && events.size() > 0) {
            Map<String, sObject> newMap = new Map<String, sObject>(), oldMap = new Map<String, sObject>();
            List<sObject> newList = new List<sObject>();
            List<Field_Audit_Trail__c> aduitRecords = new List<Field_Audit_Trail__c>();
            if (objData != null && objData.size() > 0) {
                if (String.isNotBlank(objData.get(SObjectHistoryProcessor.NEW_MAP))) {
                    newMap = (Map<String, sObject>) JSON.deserialize(objData.get(SObjectHistoryProcessor.NEW_MAP), Map<String, sObject>.class); 
                }
                if (String.isNotBlank(objData.get(SObjectHistoryProcessor.OLD_MAP))) {
                    oldMap = (Map<String, sObject>) JSON.deserialize(objData.get(SObjectHistoryProcessor.OLD_MAP), Map<String, sObject>.class);
                }
                if (String.isNotBlank(objData.get(SObjectHistoryProcessor.NEW_LIST))) {
                    newList = (List<sObject>) JSON.deserialize(objData.get(SObjectHistoryProcessor.NEW_LIST), List<sObject>.class);
                }
                //get mapping
                Map<String, FieldTrackingConfigCtrl.FieldWrapper> fieldsTracked = new Map<String, FieldTrackingConfigCtrl.FieldWrapper>();
                FieldTrackingConfig__c config = null;
                FieldTrackingConfigCtrl.Criteria qualityCriteria= null;
                if (String.isNotBlank(objAPIName)) {
                    config = getConfigData(objAPIName);
                    fieldsTracked = getFieldsTracked(config);   
                }
                if (events.get(SObjectHistoryProcessor.IS_INSERT) != null && events.get(SObjectHistoryProcessor.IS_INSERT) 
                    && events.get(SObjectHistoryProcessor.IS_AFTER) != null && events.get(SObjectHistoryProcessor.IS_AFTER)) {
                    aduitRecords = compareObjects(newMap.values(), fieldsTracked, objAPIName, qualityCriteria, currentTimeStamp, true);
                }
                if (events.get(SObjectHistoryProcessor.IS_UPDATE) != null && events.get(SObjectHistoryProcessor.IS_UPDATE)) {
                    aduitRecords = compareObjects(oldMap, newMap, fieldsTracked, objAPIName, qualityCriteria, currentTimeStamp);    
                }
                if (events.get(SObjectHistoryProcessor.IS_DELETE) != null && events.get(SObjectHistoryProcessor.IS_DELETE)) {
                    aduitRecords = compareObjects(newList, fieldsTracked, objAPIName, qualityCriteria, currentTimeStamp, false);
                }
                if (events.get(SObjectHistoryProcessor.IS_UNDELETE) != null && events.get(SObjectHistoryProcessor.IS_UNDELETE)) {
                    aduitRecords = compareObjects(newList, fieldsTracked, objAPIName, qualityCriteria, currentTimeStamp, false);    
                }       
            }
            insert aduitRecords;
        }   
    }
   
   
   private static List<Field_Audit_Trail__c> compareObjects(Map<String, sObject> oldObjs, Map<String, sObject> newObjs, Map<String, FieldTrackingConfigCtrl.FieldWrapper> fieldsTracked,  
        String objAPIName, FieldTrackingConfigCtrl.Criteria qCriteria, Datetime currentTimeStamp) {
        List<Field_Audit_Trail__c> fieldAudits = new List<Field_Audit_Trail__c>();
        if(objAPIName == 'Case' && mirCaseRecordTypeId != null) { 
            getSObjMapToGetNameForLookups(newObjs.values(), oldObjs,objAPIName);            
        }
        for (FieldTrackingConfigCtrl.FieldWrapper field : fieldsTracked.values()) {
            for (sObject newObj : newObjs.values()) {
                if (oldObjs.get(newObj.Id) != null) { 
                    sObject oldObj = oldObjs.get(newObj.Id);
                    if (String.ValueOf(oldObj.get(field.apiName)) != String.valueOf(newObj.get(field.apiName))) {
                            Field_Audit_Trail__c trail = SObjectHistoryProcessor.createAuditRecord(String.ValueOf(newObj.get('LastModifiedById')), currentTimeStamp, field.apiName, 
                                                            SObjectHistoryProcessor.getFieldValue(newObj, field, objAPIName), SObjectHistoryProcessor.getFieldValue(oldObj, field, objAPIName),  
                                                            String.ValueOf(newObj.get('Id')), objAPIName);
                            fieldAudits.add(trail);
                        
                    }   
                }   
            }   
        }
        return fieldAudits; 
   }
   
   private static List<Field_Audit_Trail__c> compareObjects(List<sObject> newObjs, Map<String, FieldTrackingConfigCtrl.FieldWrapper> fieldsTracked,  String objAPIName, 
        FieldTrackingConfigCtrl.Criteria qCriteria, Datetime currentTimeStamp, boolean isInsert) {
        List<Field_Audit_Trail__c> fieldAudits = new List<Field_Audit_Trail__c>();
        if(objAPIName == 'Case' && mirCaseRecordTypeId != null) { 
            getSObjMapToGetNameForLookups(newObjs, null,objAPIName);          
        }
        
        if (isInsert) {
            Field_Audit_Trail__c trail = SObjectHistoryProcessor.createAuditRecord(String.ValueOf(newObjs[0].get('LastModifiedById')), currentTimeStamp, CREATED, 
                                                            null, null, String.ValueOf(newObjs[0].get('Id')), objAPIName);
            fieldAudits.add(trail); 
        }
        for (FieldTrackingConfigCtrl.FieldWrapper field : fieldsTracked.values()) {
            for (sObject newObj : newObjs) {
                if (newObj.get(field.apiName) != null) {
                            Field_Audit_Trail__c trail = SObjectHistoryProcessor.createAuditRecord(String.ValueOf(newObj.get('LastModifiedById')), currentTimeStamp, field.apiName, 
                                                            SObjectHistoryProcessor.getFieldValue(newObj, field, objAPIName), null, String.ValueOf(newObj.get('Id')), objAPIName);
                            fieldAudits.add(trail); 
                }   
            }   
        }
        return fieldAudits; 
   }
   
   private static Field_Audit_Trail__c createAuditRecord(Id createdById, Datetime cDate, String field, String newVal, String oldVal, String objId, String objApiname) {
        Field_Audit_Trail__c trail = new Field_Audit_Trail__c(CreatedByID__c = createdById, CreatedDate__c = cDate, Field__c = field, NewValue__c =  newVal, 
                                        OldValue__c = oldVal, sObjectID__c = objId, SObjectAPIName__c = objApiname);
        if(objApiname == 'Case'){
           trail.Case__c = objId;
        }
        return trail;
   }
   
   private static String getFieldValue(sObject newObj, FieldTrackingConfigCtrl.FieldWrapper filed, String objAPIName) {
        String value = '';
        if (filed.fieldType.equalsIgnoreCase('datetime') && newObj.get(filed.apiName) != null) {
            value = String.ValueOf((Datetime) newObj.get(filed.apiName));   
        } else if (newObj.get(filed.apiName) != null) {
            value = String.ValueOf(newObj.get(filed.apiName));
        }
       //STRY0163815: Dipali : Added MIR recordtype condition
        if(objAPIName == 'Case' && mirCaseRecordTypeId == newObj.get('RecordTypeId')) {            
            value = populateNameInstdOfId(value);
        }
        return value;   
   }
    
   private static FieldTrackingConfig__c getConfigData(String objName) {
        Map<String, FieldTrackingConfigCtrl.FieldWrapper> fieldsTracked = new Map<String, FieldTrackingConfigCtrl.FieldWrapper>();
        List<FieldTrackingConfig__c> configs = [Select Id, Name, JSONConfigData__c, SObjectAPIName__c, DisableDelete__c, Track_Fields__c, RecordCritiera__c From FieldTrackingConfig__c 
                                                    where SObjectAPIName__c =:objName AND Track_Fields__c=true];
        return configs != null && configs.size() > 0 ? configs[0] : null;
   }
    
   private static Map<String, FieldTrackingConfigCtrl.FieldWrapper> getFieldsTracked(FieldTrackingConfig__c config) {
        Map<String, FieldTrackingConfigCtrl.FieldWrapper> fieldsTracked = new Map<String, FieldTrackingConfigCtrl.FieldWrapper>(); 
        if (config != null && String.isNotBlank(config.JSONConfigData__c)) {
            for (FieldTrackingConfigCtrl.FieldWrapper fw : (List<FieldTrackingConfigCtrl.FieldWrapper>) JSON.deserialize(config.JSONConfigData__c, List<FieldTrackingConfigCtrl.FieldWrapper>.class)) {
                fieldsTracked.put(fw.apiName, fw);
            }   
        }
        return fieldsTracked;       
   } 
   
   private static void getSObjMapToGetNameForLookups(List<sObject> newObjs,Map<String, sObject> oldObjs, String objAPIName) {
        userIdSet = new Set<Id>();
        contactIdSet = new Set<Id>();  
       //STRY0163815: Dipali : Added MIR recordtype condition 
        for (sObject newObj : newObjs) {
            if(objAPIName == 'Case' && mirCaseRecordTypeId == newObj.get('RecordTypeId')) {
                userIdSet.add(String.valueOf(newObj.get('OwnerId')));
                userIdSet.add(String.valueOf(newObj.get('CreatedById')));
                userIdSet.add(String.valueOf(newObj.get('LastModifiedById')));
                contactIdSet.add(String.valueOf(newObj.get('ContactId')));
                if(oldObjs != NULL) {
                    userIdSet.add(String.valueOf(oldObjs.get(newObj.Id).get('OwnerId')));
                    userIdSet.add(String.valueOf(oldObjs.get(newObj.Id).get('CreatedById')));
                    userIdSet.add(String.valueOf(oldObjs.get(newObj.Id).get('LastModifiedById')));
                    contactIdSet.add(String.valueOf(oldObjs.get(newObj.Id).get('ContactId')));
                }
            }
        }  
        if(userIdSet.size() > 0) {
            userMap = new Map<String, User>([SELECT Id, Name FROM User WHERE Id IN :userIdSet]);
            groupMap = new Map<String, Group>([SELECT Id, Name FROM Group WHERE Id IN :userIdSet]);
        }
        if(contactIdSet.size() > 0) {
            contactMap = new Map<String, Contact>([SELECT Id, Name FROM Contact WHERE Id IN :contactIdSet]);
        }
   }
   
   
   private static String populateNameInstdOfId(String value) {
       if(userMap != NULL && userMap.containsKey(value)) {
            value = userMap.get(value).Name;
        }
        if(groupMap != NULL && groupMap.containsKey(value)) {
            value = groupMap.get(value).Name;
        }
        if(contactMap != NULL && contactMap.containsKey(value)) {
            value = contactMap.get(value).Name;
        }
        return value; 
   }  
}