/**
 *  Class Name: ServiceEventActions_Test  
 *  Description: This is for testing of ServiceEventActions
 *  Company: Varian
 *  CreatedDate:  05/04/2000
 *
 *  Modification Log
 *  -----------------------------------------------------------
 *  Developer           Modification Date           Comments
 *  -----------------------------------------------------------  
 *  <PERSON>         May 4, 2020            Original version
 *            
 */

@isTest
public class ServiceEventActions_Test {

    @testSetup static void setup(){
        Account acct = VFSL_TestDataUtility.createAccount('Test Account 2');
        insert acct;
        
        Contact cont = VFSL_TestDataUtility.createContact(acct.Id);
        cont.RAQA_Contact__c = false;
        insert cont;
        
        ServiceTerritory st1 = VFSL_TestDataUtility.createServiceTerritory('Test Territory 2', 'TES2','Resource_Territory');
        insert st1;
        
        ServiceResource sr1 = VFSL_TestDataUtility.createServiceResource('TestResource2',st1,'Technician');
        insert sr1;
        
        Schema.Location loc = VFSL_TestDataUtility.createLocationWithArguments(acct,'Test Location');
        insert loc;
        
        Test.startTest();
        ERP_Pcode__c pcode = VFSL_TestDataUtility.getERPPCode();
        insert pcode;
         
        Product2 prod = VFSL_TestDataUtility.getProduct(pcode.Id);
        prod.Product_Type__c = 'Model';
        prod.Is_Top__c = true;
        prod.ProductCode = 'TestAssetCoverage';
        prod.Name = 'Test Asset Coverage';
        insert prod;
        
        Asset as1 = VFSL_TestDataUtility.createAssetWithArguments(acct,cont,loc,sr1,st1,'Test Asset 2');
        as1.Product2Id = prod.Id;
        insert as1;
        
        Case fslCase = VFSL_TestDataUtility.createCaseWithArguments(acct, cont, loc, as1, sr1, st1);
        fslcase.Reason = Label.VFSL_CaseReason_Maintenance;
        fslCase.Preferred_Start_Time__c = DateTime.now().addHours(1);
        fslCase.Preferred_End_Time__c = DateTime.now().addHours(2);
        fslCase.Internal_Comments__c = 'Test';
        
        Assistance_Request__c ar = new Assistance_Request__c(Case__c = fslCase.Id, Status__c = 'New');
        insert ar;
        
        Install_Assignment__c ia = new Install_Assignment__c();
        ia.Asset__c = as1.Id;
        ia.ServiceTerritory__c = st1.Id;
        ia.ServiceResource__c = sr1.Id;
        insert ia;
        
        Test.stopTest();
    }
    
    static testMethod void getCaseId(){
        Test.startTest();
        Assistance_Request__c ar = [SELECT Id FROM Assistance_Request__c LIMIT 1];
        ServiceEventActions.getAR(ar.Id);
        Test.stopTest();
    }
    
    static testMethod void getCaseIdIA(){
        Test.startTest();
        Install_Assignment__c ia = [SELECT Id FROM Install_Assignment__c LIMIT 1];
        ServiceEventActions.getIA(ia.Id);
        Test.stopTest();
    }
    
}