public class UpdateUDIWorkOrder {

    public static map<String, String> mapAssetNameVSId = new map<String, String>();

    @AuraEnabled
    public static Map<String, String> getAssetOptions(String WorkOrderId) {
        Map<String, String> mapAssetNameVSId = new Map<String, String>();

        Id headerWOLIRecordTypeId = Schema.SObjectType.WorkOrderLineItem.getRecordTypeInfosByName().get('Header').getRecordTypeId();

        
        WorkOrder WO = [
            SELECT Top_Level_Asset__c, Top_Level_Asset__r.Name, 
                Top_Level_Asset__r.Medical_Device__c, Top_Level_Asset__r.UDI_Identifier__c
            FROM WorkOrder 
            WHERE Id =: WorkOrderId 
        ];

        List<WorkOrderLineItem> woliList = [
            SELECT Id, AssetId, Asset.Name, Asset.UDI_Identifier__c
            FROM WorkOrderLineItem 
            WHERE WorkOrderId =: WorkOrderId 
            AND Asset.Medical_Device__c = true AND
            RecordTypeId =: headerWOLIRecordTypeId
        ];

        System.debug('Work Order: ' + WO);
        System.debug('Work Order Line Items: ' + woliList);

        if (WO.Top_Level_Asset__c != null && 
            WO.Top_Level_Asset__r != null && 
            WO.Top_Level_Asset__r.Medical_Device__c) {
                
                String assetNameIdentifierWO = (WO.Top_Level_Asset__r.UDI_Identifier__c != null && WO.Top_Level_Asset__r.UDI_Identifier__c != '') 
                ? WO.Top_Level_Asset__r.Name + ' ' + WO.Top_Level_Asset__r.UDI_Identifier__c 
                : WO.Top_Level_Asset__r.Name;

                String AssetId_RecorIdString = WO.Top_Level_Asset__c + '_' + WO.Id;
                system.debug('AssetId_RecorIdString WO ' + AssetId_RecorIdString);
                System.debug('assetNameIdentifierWO ' + assetNameIdentifierWO);
                mapAssetNameVSId.put(assetNameIdentifierWO, AssetId_RecorIdString);
        }

        for (WorkOrderLineItem woliVar : woliList) {
            if (woliVar.AssetId != null && woliVar.Asset != null) {
                String assetNameIdentifierWOLI = (woliVar.Asset.UDI_Identifier__c != null && woliVar.Asset.UDI_Identifier__c != '') 
                ? woliVar.Asset.Name + ' ' +  woliVar.Asset.UDI_Identifier__c 
                : woliVar.Asset.Name ;

                String AssetId_RecorIdString = woliVar.AssetId + '_' + woliVar.Id;
                system.debug('AssetId_RecorIdString WOLI ' + AssetId_RecorIdString);
                System.debug('assetNameIdentifierWOLI ' + assetNameIdentifierWOLI);
                mapAssetNameVSId.put(assetNameIdentifierWOLI, AssetId_RecorIdString);
            }
        }

        System.debug('Updated Asset Map: ' + mapAssetNameVSId);
        return mapAssetNameVSId;
    }


    @AuraEnabled
    public static string updateAssetRecords(Id assetRecId, String udiLabel, string udiPI, String deviceNumber, String RecId){

        if(deviceNumber != ''){

            if(RecId.startsWith('1WL')){
                updateWOLI(RecId, udiLabel);
            }
            else{
                updateWO(RecId, udiLabel);
            }

            List<Product_Version__c> prodVerList = [select id from Product_Version__c where UDI_Primary_DI__c = : deviceNumber];
            
            if(prodVerList?.size()>0){
                String existingUdiPI = [select id, UDI_PI__c from Asset where Id=:assetRecId]?.UDI_PI__c;
                Asset assetRec;
                
                if(existingUdiPI == null){
                    assetRec = new Asset(Id= assetRecId, UDI_Identifier__c =udiLabel, UDI_PI__c=udiPI, UDI_Last_Updated_By__c = userinfo.getuserid(), UDI_Modified_Date__c=system.today() );
                    update assetRec;
                }else{
                    assetRec = new Asset(Id= assetRecId, UDI_Identifier__c =udiLabel,  UDI_Last_Updated_By__c = userinfo.getuserid(), UDI_Modified_Date__c=system.today() );        
                    update assetRec;    
                }
                system.debug('in success');
                return 'success'; 
                }
                else{
                    system.debug('in error');
                    return 'error';
                }
        }
        else{

            if(RecId.startsWith('1WL')){
                updateWOLI(RecId, udiLabel);
            }
            else{
                updateWO(RecId, udiLabel);
            }

            String existingUdiPI = [select id, UDI_PI__c from Asset where Id=:assetRecId]?.UDI_PI__c;
            Asset assetRec;
            if(existingUdiPI == null){
                assetRec = new Asset(Id= assetRecId, UDI_Identifier__c =udiLabel, UDI_PI__c=udiPI, UDI_Last_Updated_By__c = userinfo.getuserid(), UDI_Modified_Date__c=system.today() );
                update assetRec;
            }else{
                assetRec = new Asset(Id= assetRecId, UDI_Identifier__c =udiLabel,  UDI_Last_Updated_By__c = userinfo.getuserid(), UDI_Modified_Date__c=system.today() );        
                update assetRec;    
            }
            system.debug('in success');
            return 'success'; 
        }
    }

    public static void updateWO(String WOId, String udiLabel){
        WorkOrder WORec ;
        WORec = new WorkOrder(Id=WOId, Update_Asset_UDI__c = udiLabel);
        update WORec;
        
    }
    
    public static void updateWOLI(String WOLIid, String udiLabel){
        WorkOrderLineItem woliRec;
        woliRec = new WorkOrderLineItem(Id=WOLIid, Update_Asset_UDI__c = udiLabel);
        update woliRec;
    } 


    @AuraEnabled
    public static Asset getSelectedAssetInformation(String assetRecId){
        Asset assetRec= [
            SELECT Name,Product2.name, Product_Version__r.Name, 
            UDI_Primary_DI__c, ManufactureDate, ERP_Pcode__c,
            Product_Selection_Qumulate__c
            FROM Asset 
            WHERE Id =: assetRecId
        ];

        return assetRec;
    }

    @AuraEnabled
    public static String checkForErrors (Asset assetRecord, String deviceNumber, String deviceDate, String serialNumber){
        system.debug('deviceNumber ' + deviceNumber);
        List<Asset> assetList = [
            SELECT Id, serialNumber
            FROM Asset
            WHERE ERP_Pcode__c =: assetRecord.ERP_Pcode__c AND 
            Id != : assetRecord.id
        ];

        Map<String, Asset> AssetMapWithSerialNumber = new Map<String, Asset>();
        for(Asset ast: assetList){
            AssetMapWithSerialNumber.put(ast.serialNumber, ast);
        }

        Product_Version__c productVersion = [
            SELECT Id, Name, Product__c
            FROM Product_Version__c
            WHERE UDI_Primary_DI__c =: deviceNumber LIMIT 1
        ];

        Product2 productVersionProduct = [
            SELECT Id, Name, ERP_Pcode__c
            FROM Product2
            WHERE Id =: productVersion.Product__c
        ];

        String yearPart = deviceDate.substring(0, 2);
        String monthPart = deviceDate.substring(2, 4);
        String dayPart = deviceDate.substring(4, 6);
        
        Integer month = Integer.valueOf(monthPart);
        Integer day = Integer.valueOf(dayPart);

        Boolean dateCheck = false;
        // Check if month is valid (1-12)
        if (month < 1 || month > 12) {
            dateCheck = true;
        }
        
        // Check if day is valid (1-31)
        if (day < 1 || day > 31) {
            dateCheck = true;
        }
        String errorMessage = null;

        if (AssetMapWithSerialNumber != null && AssetMapWithSerialNumber.containsKey(serialNumber)) {
            system.debug(AssetMapWithSerialNumber.get(serialNumber));
            errorMessage = 'Serial Number entered has been assigned to a different Asset';
        }
        else if ((assetRecord.ERP_Pcode__c != productVersionProduct.ERP_Pcode__c) && (deviceNumber != null && deviceNumber != '' && deviceNumber!= ' ' )) {
            errorMessage = 'DI entered does not match Asset Product';
        } 
        else if(Pattern.compile('[a-z]').matcher(serialNumber).find()){
            errorMessage = 'Serial Number cannot be in small letters';
        }  
        else if(dateCheck){
            errorMessage = 'Date entered is not in the required date format YYMMDD';
        }
        else {
            errorMessage = null;
        }
        system.debug('errorMessage--- ' + errorMessage);
        return errorMessage;
    }

}