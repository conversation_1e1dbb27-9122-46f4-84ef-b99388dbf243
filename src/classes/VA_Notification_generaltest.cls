public class VA_Notification_generaltest {
    public static void sendPushNotification(List<ContentVersion> contentVersions) {
        System.debug('Start of sendPushNotification method');
        
        if (contentVersions == null || contentVersions.isEmpty()) {
            System.debug('Content versions list is empty or null.');
        }
        
        for(ContentVersion cv : contentVersions) {
            System.debug('Processing ContentVersion: ' + cv.Id);

            Messaging.PushNotification msg = new Messaging.PushNotification();
            Map<String, Object> payload = new Map<String, Object>();
            payload.put('title', 'Apex Notification!');
            payload.put('Message', 'You have sent a custom notification.');
            
            msg.setPayload(payload);
            System.debug('Payload set: ' + payload);

            String userId1 = cv.OwnerId;
            String userId2 = cv.LastModifiedById;
            System.debug('OwnerId: ' + userId1 + ', LastModifiedById: ' + userId2);

            Set<String> users = new Set<String>();
            if (userId1 != null) {
                users.add(userId1);
                System.debug('Added userId1: ' + userId1);
            }
            if (userId2 != null) {
                users.add(userId2);
                System.debug('Added userId2: ' + userId2);
            }

            System.debug('Users to notify: ' + users);

            msg.send('VA_Push_Notification', users);
            System.debug('Push notification sent to users: ' + users);
        }
        
        System.debug('End of sendPushNotification method');
    }
}