public class VIC_Case_TriggerHandler {
    
    //@future
    public static void handleUpdate(Set<ID> caseIds){
        
        List<Case> caseList = [Select Id,Parent__c,Parent_Billing_Type__c,
                               		Asset_Top_Level__r.Product__c,Asset_Top_Level__r.Billing_Type_Text__c
                               From Case Where Id in :caseIds];
        List<Case> caseListUpdate = new List<Case>();
        
        for(Case cs :caseList){
            cs.Parent__c = cs.Asset_Top_Level__r.Product__c;
            cs.Parent_Billing_Type__c = cs.Asset_Top_Level__r.Billing_Type_Text__c;
            caseListUpdate.add(cs);
        }
        
        update caseListUpdate;
            
    }

}