public without sharing class VIC_CreateGroup {
    @AuraEnabled
    public static void getCreateGroup(CollaborationGroup massge) {
        try{
            List<String> emailList = new List<String>();
            //Rakesh:Start
            String VIC_Request_New_Community = System.Label.VIC_Request_New_Community;
            if(VIC_Request_New_Community != null){
                emailList = VIC_Request_New_Community.split(',');
            }
            /*emailList.add('<EMAIL>');
            emailList.add('<EMAIL>');
            emailList.add('<EMAIL>');*/
            //Rakesh:End
            User loginUser = [Select Id,email, ContactId, Contact.Account_Name__c, Contact.AccountId ,Contact.Name from User where id = :Userinfo.getUserId() limit 1];
            Id currentContactId = loginUser.ContactId;
            contact c=new contact();
            c.Id=currentContactId;
            c.Proposed_Community_Name__c=massge.Name;
            c.Proposed_Community_Description__c=massge.Description;
            update c;
            
            EmailTemplate  emailTemplate = [Select Id,Subject,Description,HTMLValue,DeveloperName,Body from EmailTemplate where DeveloperName  ='VIC_Request_New_Community'];
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setTargetObjectId(currentContactId);
            email.setToAddresses(emailList);
            email.setTemplateId(emailTemplate.Id);   
            
            Messaging.SingleEmailMessage[] messages =   new List<Messaging.SingleEmailMessage> {email};
                Messaging.SendEmailResult[] results = Messaging.sendEmail(messages);
        }
        catch(Exception e){}
    }
    
    @AuraEnabled(cacheable=true)
    public static ArticleDataTableWrapper getarticle(String frequency, Decimal pageNumber, Decimal pageSize) {
        System.debug('frequency======'+frequency);
        System.debug('pageNumber======'+pageNumber);
        System.debug('pageSize======'+pageSize);
        Id krecTypeObj=Schema.SObjectType.Service_Article__kav.getRecordTypeInfosByDeveloperName().get('Blogs').getRecordTypeId();
        Integer pSize = (Integer)pageSize;
        Integer pNumber = (Integer)pageNumber;
        
        //Offset for SOQL
        Integer offset = (pNumber - 1) * pSize;
        
        //Total Records
        Integer totalRecords = 0;
        if(frequency == 'LAST_N_MONTHS:3') {
            totalRecords = [select count() from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:3 OR CreatedDate = THIS_MONTH)];
        } else if(frequency == 'LAST_N_MONTHS:6') {
            totalRecords = [select count() from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:6 OR CreatedDate = THIS_MONTH)];
        } else {
            totalRecords = [select count() from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And CreatedDate = THIS_MONTH];
        }
        Integer recordEnd = pSize * pNumber;
        
        //Instance of Article DataTable Wrapper Class
        ArticleDataTableWrapper objDT =  new ArticleDataTableWrapper();  
        objDT.pageSize = pSize;
        objDT.pageNumber = pNumber;
        objDT.recordStart = offset + 1;
        objDT.recordEnd = totalRecords >= recordEnd ? recordEnd : totalRecords;
        objDT.totalRecords = totalRecords;
        
        try {
            list<Service_Article__kav> knowList=new list<Service_Article__kav>();
            map<Id,string>  sarIds= new map<Id,string>();
            set<String> ownerIds = new set<String>(); 
            
            List<Service_Article__kav> kvs = new List<Service_Article__kav>();
            System.debug('frequency======'+frequency);
            if(frequency == 'LAST_N_MONTHS:3') {
                kvs = [select Id,Article_Feedback_Link__c,Title,UrlName,Summary,Process__c,Workaround__c,ArticleTotalViewCount,CreatedDate,OwnerId,Owner.Name from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:3 OR CreatedDate = THIS_MONTH) ORDER BY LastModifiedDate DESC LIMIT :pSize OFFSET :offset];
            } else if(frequency == 'LAST_N_MONTHS:6') {
                kvs = [select Id,Article_Feedback_Link__c,Title,UrlName,Summary,Process__c,Workaround__c,ArticleTotalViewCount,CreatedDate,OwnerId,Owner.Name from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:6 OR CreatedDate = THIS_MONTH) ORDER BY LastModifiedDate DESC LIMIT :pSize OFFSET :offset];
            } else {
                kvs = [select Id,Article_Feedback_Link__c,Title,UrlName,Summary,Process__c,Workaround__c,ArticleTotalViewCount,CreatedDate,OwnerId,Owner.Name from Service_Article__kav where  RecordTypeId =:krecTypeObj AND PublishStatus = 'Online' And CreatedDate = THIS_MONTH ORDER BY LastModifiedDate DESC LIMIT :pSize OFFSET :offset];
            }
            
            for(Service_Article__kav sar : kvs) {
                knowList.add(sar);
                ownerIds.add(sar.OwnerId);
            }
            
            for(User u:[ Select ID,Name, SmallPhotoUrl, FullPhotoUrl From User WHERE ID=:OwnerIds]){
                sarIds.put(u.Id,u.FullPhotoUrl);
            }
            
            for(Service_Article__kav sar:knowList){
                sar.Article_Image__c=sarIds.get(sar.OwnerId);
            }
            System.debug('articleList=='+knowList);
            objDT.articleList = knowList;
            return objDT;
        } catch(Exception e) {
            return null; 
        }
    }
    
    @AuraEnabled
    public static List<Service_Article__kav> displayarticle( string recordids ) {
        return [select Id,Title,UrlName,Workaround__c,Process__c,Manager_Name__c,Answer__c from Service_Article__kav where id =: recordids];
    }
    
    @AuraEnabled
    public static void getCreateBlogs(Service_Article__kav blogs){
        String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        String guid = '';
        while (guid.length() < 5) {
            Integer idx = Math.mod(Math.abs(Crypto.getRandomInteger()), chars.length());
            guid += chars.substring(idx, idx+1);
        }
        
        Id krecTypeObj=Schema.SObjectType.Service_Article__kav.getRecordTypeInfosByDeveloperName().get('Blogs').getRecordTypeId();
        Service_Article__kav blogsArt=new Service_Article__kav();
        blogsArt.Title= blogs.Title;
        blogsArt.Summary=  blogs.Summary ;
        blogsArt.Process__c=blogs.Process__c;
        blogsArt.UrlName= blogs.Title.substring(0,4)+ '-' +guid;
        blogsArt.RecordTypeId=krecTypeObj;
        
        insert blogsArt;
        system.debug('blogsArt');
    }
    
    @AuraEnabled
    public static List<Service_Article__kav> getRecordTypeArticle( string Ids ) {
        list<Service_Article__kav> Recordty=[select Id,RecordType.Developername from Service_Article__kav where id =: Ids];
        if(Recordty.size()>0) {
            return [select Id,Title,UrlName,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c from Service_Article__kav where RecordType.Developername =: Recordty[0].RecordType.Developername AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:12 OR CreatedDate = THIS_MONTH) ORDER BY ArticleTotalViewCount DESC limit 4];
        } else {
            return [select Id,Title,UrlName,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c from Service_Article__kav where RecordType.Developername = 'Blogs' AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:12 OR CreatedDate = THIS_MONTH) ORDER BY ArticleTotalViewCount DESC limit 4];
        }
    }
    
    @AuraEnabled
    public static List<FeedItem> getTrendingArticles() {
        Id netId=[select id, name from network where name='Varian Innovation Center'].Id;
        return [SELECT Id, CommentCount, LikeCount, LinkUrl, NetworkScope, ParentId, RelatedRecordId, Status, Title, Type, Parent.Profile.Name, Visibility FROM FeedItem 
                where Title!='' AND Type='QuestionPost' AND NetworkScope=:netId ORDER BY LikeCount DESC limit 4];
    }
    
    @AuraEnabled
    public static List<Service_Article__kav> getPopularArticle( string ArtId ) {
        
        list<Service_Article__kav> Recordty=[select Id,RecordType.Developername from Service_Article__kav where id =: ArtId];
        if(Recordty.size()>0) {
            return [select Id,Title,UrlName,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c,CreatedDate from Service_Article__kav where RecordType.Developername =: Recordty[0].RecordType.Developername AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:12 OR CreatedDate = THIS_MONTH) ORDER BY CreatedDate DESC  limit 4 ];
        } else {
            return [select Id,Title,UrlName,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c from Service_Article__kav where RecordType.Developername = 'Blogs' AND PublishStatus = 'Online' And (CreatedDate = LAST_N_MONTHS:12 OR CreatedDate = THIS_MONTH) ORDER BY CreatedDate DESC limit 4];  
        }
    }
    
    @AuraEnabled
    public static PermissionSetAssignment getPermissionCheck( ) {
        try{
            
            return [  SELECT Id FROM PermissionSetAssignment WHERE AssigneeId = :Userinfo.getUserId() AND PermissionSet.Name = 'VIC_Community_Internal_Admin'];
        }catch(Exception e){
            return null;
        }
    }
    
    @AuraEnabled
    public static List<Service_Article__kav> getFilterBlogs( string conFil,string blogId ) {
        String vicOrderQuery;
        Service_Article__kav Recordty=[select Id,RecordType.Developername from Service_Article__kav where id =: blogId];
        // system.debug('@@Recordty'+ Recordty.RecordType.Developername);
        List<Service_Article__kav> lstArtcleBlog;
        system.debug('@@conFil'+ conFil);
        if(conFil=='LAST_N_MONTHS:12'){
            lstArtcleBlog = [select Id,Title,UrlName,OwnerId,Summary,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c,CreatedDate from Service_Article__kav where RecordType.Developername =: Recordty.RecordType.Developername And (CreatedDate = LAST_N_MONTHS:12 OR CreatedDate = THIS_MONTH)  AND PublishStatus = 'Online' Order By LastmodifiedDate desc];
        } else if(conFil=='LAST_N_MONTHS:3'){
            lstArtcleBlog = [select Id,Title,UrlName,OwnerId,Summary,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c,CreatedDate from Service_Article__kav where RecordType.Developername =: Recordty.RecordType.Developername And (CreatedDate = LAST_N_MONTHS:3 OR CreatedDate = THIS_MONTH)  AND PublishStatus = 'Online' Order By LastmodifiedDate desc];
        } else if(conFil=='LAST_N_MONTHS:6'){
            lstArtcleBlog = [select Id,Title,Summary,OwnerId,UrlName,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c,CreatedDate from Service_Article__kav where RecordType.Developername =: Recordty.RecordType.Developername And (CreatedDate = LAST_N_MONTHS:6 OR CreatedDate = THIS_MONTH)  AND PublishStatus = 'Online' Order By LastmodifiedDate desc];
        } else {
            lstArtcleBlog = [select Id,Title,OwnerId,UrlName,Summary,Workaround__c,RecordType.Developername,Process__c,ArticleTotalViewCount,Manager_Name__c,Answer__c,CreatedDate from Service_Article__kav where RecordType.Developername =: Recordty.RecordType.Developername  AND PublishStatus = 'Online' And CreatedDate = THIS_MONTH Order By LastmodifiedDate desc];
        }
        
        list<Service_Article__kav> knowListPic=new list<Service_Article__kav>();
        for(Service_Article__kav sar : lstArtcleBlog){
            user uPic=[ Select ID,Name, SmallPhotoUrl, FullPhotoUrl From User WHERE ID=:sar.OwnerId] ;
            sar.Article_Image__c=uPic.FullPhotoUrl;
            knowListPic.add(sar);
        }
        return knowListPic;
    }
    
    public class ArticleDataTableWrapper {
        @AuraEnabled
        public Integer pageSize {get;set;}
        @AuraEnabled
        public Integer pageNumber {get;set;}
        @AuraEnabled
        public Integer totalRecords {get;set;}
        @AuraEnabled
        public Integer recordStart {get;set;}
        @AuraEnabled
        public Integer recordEnd {get;set;}
        @AuraEnabled
        public List<Service_Article__kav> articleList {get;set;}
    }
}