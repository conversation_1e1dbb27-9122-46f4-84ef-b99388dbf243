public class vMC_LogController_New {
    public static final String CLASSNAMES = 'vMC_LogController'; 
    
    public static vMarket_Log__c createLogs(String type, String responseBody, HttpRequest request) {
        vMarket_Log__c logs = new vMarket_Log__c();
        
        vMarket_JSONParser parser = vMarket_JSONParser.parse(responseBody);
        system.debug(' === ' + parser);
        system.debug('Inside Error' + parser.Error);
        if( parser.object_Z == 'charge') {
            logs.Amount__c = parser.amount/100;
            logs.Status__c = parser.status;
        } else if( parser.Error != null) {
            system.debug('Inside Error');
            logs.Error_Message__c = parser.Error.message;   logs.Error_Destination__c = parser.Error.param;
            logs.Error_type__c = parser.error.type_Z;  logs.Status__c = 'Error';
        }
        logs.Charge_Id__c = parser.id;
        logs.Application_Fee__c = parser.application_fee; 
        logs.Balance_Transaction__c = parser.balance_transaction;
        logs.Currency__c = parser.currency_z;
        logs.Destination__c = parser.description;
        logs.Destination_Id__c = parser.destination;
        logs.On_Behalf_Of__c = parser.on_behalf_of;
        system.debug('parser.status ' + parser.status );
        
        logs.Transfer__c = parser.transfer;
        logs.Transfer_Group__c = parser.transfer_group;
        logs.RequestRawData__c = request.getBody();
        logs.ResponseRawData__c = responseBody;
        system.debug(' =======parser.amount========== ' + parser.amount);
        system.debug(' ================= ' + logs);//system.assert(false);
        return logs;
    }
    
}