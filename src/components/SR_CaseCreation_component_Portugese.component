<apex:component controller="SR_ComponentClass" access="global">

<apex:attribute name="Portugese" description="Case creation Email" type="Id" required="required" assignTo="{!controllerValue}"/>
<style>
.col1{
    width:20%;
    position:relative;
}

.col2{
    width:80%;
    position:relative;
}

</style>

<table border="0" style="background-color : #FFFFFF;width:100%;font-size:13px; font-family:arial;" cellpadding="2" cellspacing ="0">
     
     <tr style="text-align:center;" >
      <td colspan="2">
      <tr><td>
      <apex:image value="{!$Label.VarianCustomerSupport}" style="width:90%;" id="LogoImage"/>
      </td>
      </tr>
      
       
      
     </td>
     </tr>

    <!--apex:image value="{!$Label.Varian_Partner_for_life_logo}"  style="width:auto;" / background-image:url({!$Label.Varian_Partner_for_life_logo});-->

    <tr style="text-align:left;">
          <td colspan="2">
          <style type="text/css"> 
                p { color:#fff!mportant
                   font-weight: bold;
                   font-size: 75px;
                   size  }
                </style>
        <div style="font-size:15px;">
        <b><span style="color: #0000FF;font-size:15px;"> Caso nº:</span></b> {!RecCase.CaseNumber}
     
            
        </div>      
         <br/>
        <br/>
        <br/>Caro (a) {!RecCase.Contact.Name}, <br/><br/>
        Obrigado por contatar a Varian Medical Systems. O seguinte caso foi criado de acordo com vossa vossa solicitação <br/><b/><br/>
        </td>
    </tr>
    <tr style="text-align:left;" >
      <td colspan="2">

    <b><u><span style ="color:#0000FF;">Informação do caso</span></u></b><br/>
    Identificação do Cliente: {!RecCase.Site_ID__c}<br/>
    Estado: Caso em aberto<br/>
    Produto: {!RecCase.Asset.name} <br/>
    Data de Criação: {!DateToDisplay} <br/><br/>


    <b><u><span style ="color:#0000FF;">Tema:</span></u></b><br/>
    {!RecCase.Subject} <br/><br/>

    <br>
    Caso possua uma conta MyVarian e deseje atualizar ou consultar o estado do seu caso, clique em  “Ver Caso” <a href="{!$Label.portal_creation_redirection_url}{!RecCase.id}">“View Case”</a>,  em alternativa pode responder diretamente a este email. </br>
    
    <br/><br/>
    <div style="text-align:left;"><b>Nota:</b> 
   Com a sua permissão, acessaremos ao seu sistema usando a ferramenta de acesso remoto “SmartConnect”, com o objetivo de auxiliar a investigação e resolução do problema. A nossa intenção é manter a comunicação com o Sr.(a) durante este caso durante esta ligação. Caso esta ligação remota seja efetuada fora do horário comercial, enviaremos um resumo das ações realizadas com o objetivo de
   investigar e resolver o caso em aberto.
    </div>
      </td>
    </tr>


    </TABLE>
    </apex:component>