<template>
    <div class="slds-var-p-around_xxx-small">
        <lightning-card title="Visibility Check" icon-name="standard:orchestrator">
            <template if:true={spinner}>
                <lightning-spinner 
                    variant="brand" 
                    size="large" 
                    alternative-text="Checking...">
                </lightning-spinner>
            </template>
            <div class="slds-grid slds-gutters slds-p-around_small slds-m-bottom_x-small">
                <div class="slds-col slds-size_1-of-3 slds-var-p-around_xx-small">
                    <div class="slds-grid slds-gutters">
                        <div class="slds-col slds-size_7-of-8 slds-align-left">
                            <lightning-combobox
                                label="Input Mode"
                                name="Input"
                                value={inputType}
                                options={typeOptions}
                                onchange={typeChange}
                            >
                            </lightning-combobox>
                        </div>
                    </div>
                    <template lwc:if={displaySimple}>
                        <div class="slds-grid slds-gutters">
                            <div class="slds-col slds-size_7-of-8">
                                <lightning-input
                                    type="text" 
                                    label="User Id"
                                    placeholder="Enter User Id (18 char)"
                                    onchange={userIdChanged}
                                    value={simpleUserId}
                                    minlength="18"
                                    maxlength="18"
                                    required
                                >
                                </lightning-input>
                            </div>
                        </div>
                        <div class="slds-grid slds-gutters">
                            <div class="slds-col slds-size_7-of-8">
                                <lightning-input
                                    type="text" 
                                    label="Record Id - for Asset use the Parent Account Id"
                                    placeholder="Enter Record Id (18 char)"
                                    onchange={recordIdChanged}
                                    value={simpleRecordId}
                                    minlength="18"
                                    maxlength="18"
                                    required
                                >
                                </lightning-input>
                            </div>
                            <div class="slds-col slds-size_1-of-8 slds-align-bottom slds-align-right">
                                <lightning-button-icon
                                    title="Check Record Access"
                                    alternative-text="Check Record Access"
                                    onclick={checkObjectAccess}
                                    variant="brand"
                                    icon-name="utility:locker_service_console"
                                >
                                </lightning-button-icon> 
                            </div>
                        </div>
                    </template>
                    <template lwc:else>
                        <div class="slds-grid slds-gutters">
                            <div class="slds-col slds-size_7-of-8 slds-align-left">
                                <lightning-record-picker
                                    label="Select User"
                                    placeholder="Search Active Users"
                                    object-api-name="User"
                                    filter={filter}
                                    display-info={displayInfo}
                                    matching-info={matchingInfo}
                                    onchange={userChanged}
                                    value={userId}
                                >
                                </lightning-record-picker>
                            </div>
                        </div>
                        <template lwc:if={userSelected}>
                            <div class="slds-grid slds-gutters">
                                <div class="slds-col slds-size_7-of-8">
                                    <lightning-input
                                        type="text" 
                                        label="Object Name - Case Sensitive - for Asset use 'Account'"
                                        placeholder="Enter Object API Name (Case Sensitive)"
                                        onchange={objectChanged}
                                    >
                                    </lightning-input>
                                </div>
                                <div class="slds-col slds-size_1-of-8 slds-align-bottom slds-align-right">    
                                    <lightning-button-icon
                                        title="Check Object Validity"
                                        alternative-text="Check Object Validity"
                                        onclick={checkObject}
                                        variant="neutral"
                                        icon-name="utility:check"
                                    >
                                    </lightning-button-icon>
                                </div>
                            </div>
                        </template>
                        <template lwc:if={objectValid}>
                            <div class="slds-grid slds-gutters">
                                <div class="slds-col slds-size_7-of-8">
                                    <lightning-record-picker
                                        label="Select Record - for Asset select parent Account"
                                        placeholder={recordSearchPlaceholder}
                                        object-api-name={objectName}
                                        display-info={displayInfoRecord}
                                        matching-info={matchingInfoRecord}
                                        onchange={recordChanged}
                                    >
                                    </lightning-record-picker>
                                </div>
                                <div class="slds-col slds-size_1-of-8 slds-align-bottom slds-align-right">
                                    <lightning-button-icon
                                        title="Check Object Access"
                                        alternative-text="Check Object Access"
                                        onclick={checkObjectAccess}
                                        variant="brand"
                                        icon-name="utility:locker_service_console"
                                    >
                                    </lightning-button-icon> 
                                </div>
                            </div>
                        </template>
                    </template>
                </div>
                <div class="slds-col slds-size_2-of-3 slds-var-p-around_xx-small">
                    <template lwc:if={showObjectAccess}>
                        <div class="slds-var-p-around_xx-small">
                            <lightning-datatable
                                key-field="id" 
                                data={objectAccess} 
                                columns={columnsAccess} 
                                hide-checkbox-column="true"
                            >
                            </lightning-datatable>
                        </div>
                        <div class="slds-m-top_x-small slds-var-p-around_xx-small">
                            <lightning-button
                                label="Explain Access"
                                variant="Success"
                                class="slds-align_absolute-center"
                                onclick={explainAccess}
                            >
                            </lightning-button>
                        </div>
                    </template>
                </div>
            </div>
            <template lwc:if={showAccessReason}>
                <div class="slds-border_top slds-p-top_xx-small">
                    <c-user-access-reason
                        record-id = {recordId}
                        user-id = {userId}
                        max-access = {maxAccess}
                    >
                    </c-user-access-reason>
                </div>
            </template>
        </lightning-card>
    </div>
</template>