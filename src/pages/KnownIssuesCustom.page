<!--***************************************************************************
Change Log:
Name/Task or Story or Inc # /Description of Change
Dipali - STRY0116730 - Created.
*************************************************************************************-->
<apex:page standardController="Idea" extensions="KnownIssuesCustomCtrl" >
    <iframe id="my_iframe" style="display:none;"></iframe>
    <apex:sectionHeader title="Known Issue" subtitle="{!ideaObj.Title}"/>
    <apex:form id="theform">
        
        <style>
            .pageBlock2{ 
            background-color: transparent !important;
            border: 0px !important;
            }
            .tableTD{
            padding:5px;
            }
            .tableTR{
            border-bottom:1px solid #ECECEC;
            }
            .tableHeadTD{
            padding:10px;
            Font-size:16px;
            font-weight:bold;
            }
            .promoted{
            padding:10px;
            text-align:center;
            }
            .downloadIcon{
            background-image: url('{!URLFOR($Resource.IdeaInternalSR,'Images/AttachmentDownload.png')}');
            }
            .htmlDetailElementIframe{
            display: none;
            }
        </style>
        
        <apex:actionfunction action="{!deleteIdea}" name="deleteIdea" rerender="mainBlock"/>
        <apex:actionfunction action="{!DeleteComment}" name="deleteIdeaComment" rerender="theform">
            <apex:param value="" name="CommentId"/>
        </apex:actionfunction>
        <apex:actionfunction action="{!DeleteInternalComment}" name="deleteInternalIdeaComment" rerender="theform">
            <apex:param value="" name="CommentId"/>
        </apex:actionfunction>
        <div id="backgroundPopup"></div> 
        <apex:pageMessages />
        <apex:pageBlock id="mainBlock">
            <apex:pageBlockButtons location="top">
                <apex:commandButton value="Edit" immediate="true" action="{!EditIdea}"  /> <!-- disabled="{!IF(obj.IdeaObj.Status == 'Converted to Case' ,true,false)}" -->
                <apex:commandButton value="Delete" onclick="return confirmDeleteJS()"  immediate="true" rerender="mainBlock"/>
                <apex:commandButton value="Close" action="/apex/CustomIdeaListView?isKnownIssues=true"  immediate="true"/>
            </apex:pageBlockButtons>
            <table style="width:100%;border-top:1.5px solid #8a9ebe;">
                <tr>
                    <td class="tableHeadTD" colspan="2">
                        Known Issue Information
                    </td>
                </tr>
                <tr>
                    <td class="tableTD" style="width:50% !important">
                        <table style="border-collapse: collapse;">
                            <tr class="tableTR">
                                <td class="tableTD">Known Issue ID</td>
                                <td  class="tableTD"><apex:outputField value="{!obj.IdeaObj.Product_Idea_ID__c}"/> </td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Title</td>
                                <td class="tableTD"><apex:outputfield value="{!obj.IdeaObj.Title}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Description</td>
                                <td class="tableTD"><apex:outputfield value="{!obj.IdeaObj.body}"/></td>
                            </tr>
                            <!--STRY0126429: Dipali: Added Recommendation field-->
                            <tr class="tableTR">
                                <td class="tableTD">Recommendation</td>
                                <td class="tableTD"><apex:outputfield value="{!obj.IdeaObj.Recommendation__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Product Group</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.Categories}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Affected Version</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.Product_Version__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Complaint Number(CP #)</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.Complaint_Number_CP__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Reference Number(DR #)</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.ClearQuest_Number__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Status</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.Status }"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Release Version</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.Release_Version__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Attachments<div class="downloadIcon"/></td>
                                <td class="tableTD">
                                    <!-- STSK0012697.Display all attachments of Idea -->
                                    <table>
                                        <apex:repeat value="{!lstIdeaAttachment}" var="IdeaAttachment">
                                            <tr onclick="DownloadFile('{!IdeaAttachment.Id}')">
                                                <td><apex:image url="{!URLFOR($Resource.IdeaInternalSR, 'Images/AttachmentDownload.png')}" 
                                                                style="margin-right:5px;width: 16px; height: 16px;cursor: pointer;"/>
                                                </td>
                                                <td style="cursor: pointer;">
                                                    <apex:outputField value="{!IdeaAttachment.Name}" />
                                                </td>
                                            </tr>
                                        </apex:repeat>
                                    </table>                                   
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td style="vertical-align:middle;text-align:left;">
                        <input type="button" Class="btn" value="{!CommentCount}"  style="padding: 25px; background-image: none;cursor: default;"/>
                    </td>
                </tr>
            </table>
            <br/>
            <table style="width:100%;border-top:1.5px solid #8a9ebe;">
                <tr >
                    <td class="tableHeadTD" colspan="2">
                        Submitter Details
                    </td>
                </tr>
                <tr>
                    <td class="tableTD" style="width:50%">
                        <table style="border-collapse: collapse;">
                            <tr class="tableTR">
                                <td class="tableTD">User Submitted</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.User_Submitted__c}"/></td>
                            </tr>
                            <tr class="tableTR">
                                <td class="tableTD">Last Modified By</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.lastmodifiedById}"/>,&nbsp;<apex:outputField value="{!obj.IdeaObj.LastModifiedDate}"/></td>
                            </tr>
                        </table>
                    </td>
                    <td class="tableTD" style="width:50%">
                        <table style="border-collapse: collapse;">
                            <tr class="tableTR">
                                <td class="tableTD">Created Date</td>
                                <td class="tableTD"><apex:outputField value="{!obj.IdeaObj.createdDate}"/></td>
                            </tr>   
                        </table>
                    </td>
                </tr>
            </table>
            <br/>
            
            <br/>
            <apex:outputpanel rendered="{!!isFindLayout}">
                                <!-- <table style="width:100%;border-top:3px solid #178AFF;border-bottom:1px solid #E5E5E5;border-left:1px solid #E5E5E5;border-right:1px solid #E5E5E5;border-collapse: collapse;">
                <tr style="border-bottom:1px solid #ECECEC;">
                <td class="tableHeadTD" colspan="1" style="width: 30%;">
                Comments
                </td>
                <td class="tableHeadTD" colspan="1">                        
                <apex:commandLink value="Add Your Comment" action="{!AddNewComment}" style="font-size: 14px;" />
                </td>
                </tr>
                <tr>
                <td class="tableTD" colspan="2">
                <table style="border-collapse: collapse;width:100%">
                <apex:repeat value="{!obj.lstComments}" var="c">
                <tr class="tableTR">
                <td class="tableTD" style="width:200px;"> 
                <apex:commandLink action="{!EditComment}" value="Edit" style="color:#015BA7;font-weight:bold;text-decoration: none;">
                <apex:param name="CommentId" value="{!c.Id}"/>
                </apex:commandLink>
                &nbsp;|&nbsp;
                <a href="javascript:void(0)" onclick="return confirmDeleteCommentJS('{!c.Id}')" style="color:#015BA7;font-weight:bold;text-decoration: none;">Del</a>
                </td>
                <td class="tableTD">
                <div>{!c.CommentBody}</div>
                
                </td>
                <td class="tableTD" style="width:200px;">
                <div><apex:commandLink value="{!c.createdBy.Name}" style="color:#015BA7;font-weight:bold;text-decoration: none;"/></div>
                <div style="font-family: Arial,Helvetica,sans-serif;color: rgb(102, 102, 102);">
                <apex:outputText value="{0, date, MM/dd/yyyy hh:mm a}">
                <apex:param value="{!c.createdDate}" /> 
                </apex:outputText>
                </div>
                </td>
                </tr>
                </apex:repeat>
                </table>
                </td>
                
                </tr>
                </table>-->
                                <!--<br/>   
                <apex:outputpanel rendered="{!NewCommentEnable}">
                <table style="width:100%;border-top:3px solid #178AFF;border-bottom:1px solid #E5E5E5;border-left:1px solid #E5E5E5;border-right:1px solid #E5E5E5">
                <tr>
                <td class="tableHeadTD" colspan="2">
                Add Your Comment
                </td>
                </tr>
                <tr>
                <td class="tableTD">
                Comment
                </td>
                <td class="tableTD">
                <apex:inputField value="{!newComment.commentBody}" required="true"/>
                </td>
                </tr>
                <tr>
                <td class="tableTD">
                &nbsp;
                </td>
                <td class="tableTD">
                <apex:commandButton value="Post" action="{!saveNewComment}"/>
                </td>
                </tr>
                </table>
                </apex:outputpanel>
                <br/>-->
                <table style="width:100%;border-top:3px solid #178AFF;border-bottom:1px solid #E5E5E5;border-left:1px solid #E5E5E5;border-right:1px solid #E5E5E5;border-collapse: collapse;">
                    <tr style="border-bottom:1px solid #ECECEC;">
                        <td class="tableHeadTD" colspan="1" style="width: 30%;">
                            Internal Comments
                        </td>
                        <td class="tableHeadTD" colspan="1">                        
                            <apex:commandLink value="Add Your Comment" action="{!AddNewInternalComment}" style="font-size: 14px;" />
                        </td>
                    </tr>
                    <tr>
                        <td class="tableTD" colspan="2">
                            <table style="border-collapse: collapse;width:100%">
                                <apex:repeat value="{!obj.lstInternalComments}" var="c">
                                    <tr class="tableTR">
                                        <td class="tableTD" style="width:200px;"> 
                                            <apex:commandLink action="{!EditComment}" value="Edit" style="color:#015BA7;font-weight:bold;text-decoration: none;">
                                                <apex:param name="CommentId" value="{!c.Id}"/>
                                                <apex:param name="isinternal" value="yes"/>
                                            </apex:commandLink>
                                            &nbsp;|&nbsp;
                                            <a href="javascript:void(0)" onclick="return confirmDeleteInternalCommentJS('{!c.Id}')" style="color:#015BA7;font-weight:bold;text-decoration: none;">Del</a>
                                        </td>
                                        <td class="tableTD">
                                            <div>{!c.Comment__c}</div>
                                        </td>
                                        <td class="tableTD" style="width:200px;">
                                            <div><apex:commandLink value="{!c.createdBy.Name}" style="color:#015BA7;font-weight:bold;text-decoration: none;"/></div>
                                            <div style="font-family: Arial,Helvetica,sans-serif;color: rgb(102, 102, 102);">
                                                <apex:outputText value="{0, date, MM/dd/yyyy hh:mm a}">
                                                    <apex:param value="{!c.createdDate}" /> 
                                                </apex:outputText>
                                            </div>
                                        </td>
                                    </tr>
                                </apex:repeat>
                            </table>
                        </td>
                    </tr>
                </table>
                <br/>   
                <apex:outputpanel rendered="{!NewInternalCommentEnable}">
                    <table style="width:100%;border-top:3px solid #178AFF;border-bottom:1px solid #E5E5E5;border-left:1px solid #E5E5E5;border-right:1px solid #E5E5E5">
                        <tr>
                            <td class="tableHeadTD" colspan="2">
                                Add Your Comment
                            </td>
                        </tr>
                        <tr>
                            <td class="tableTD">
                                Comment
                            </td>
                            <td class="tableTD">
                                <apex:inputField value="{!newInternalComment.Comment__c}" required="true"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="tableTD">
                                &nbsp;
                            </td>
                            <td class="tableTD">
                                <apex:commandButton value="Post" action="{!saveNewInternalComment}"/>
                            </td>
                        </tr>
                    </table>
                </apex:outputpanel>
                <br/>
                <br/>
                <table style="width:100%;border-top:3px solid #178AFF;border-bottom:1px solid #E5E5E5;border-left:1px solid #E5E5E5;border-right:1px solid #E5E5E5;border-collapse: collapse;">
                    <tr style="border-bottom:1px solid #ECECEC;">
                        <td class="tableHeadTD" colspan="2">
                            Last 100 Votes
                        </td>
                    </tr>
                    <tr>
                        <td class="tableTD" colspan="2">
                            <table style="border-collapse: collapse;width:100%">
                                <apex:repeat value="{!IdeaVote}" var="c">
                                    <tr class="tableTR">
                                        <td class="tableTD"> 
                                            <apex:outputfield value="{!c.CreatedById}"/>
                                        </td>                                       
                                    </tr>
                                </apex:repeat>
                            </table>
                        </td>
                    </tr>
                </table>
                <br/>
            </apex:outputpanel>         
        </apex:pageBlock>
    </apex:form>
    <script>
    
    
    function DownloadFile(attId) {
        var url = '{!InstanceURL}/servlet/servlet.FileDownload?file='+attId;                                                    
        var win = window.open(url, '_blank');  win.focus();
    }
    
    function confirmDeleteJS(){
        var isDelete = confirm("Deleting this item is irreversible. Touch OK to continue or CANCEL to abort.");
        if (isDelete) {
            deleteIdea();
            return true;
        }
        return false;
    }
    
    function confirmDeleteCommentJS(objid){
        var isDelete = confirm("Deleting this item is irreversible. Touch OK to continue or CANCEL to abort.");
        if (isDelete) {
            deleteIdeaComment(objid);
            return true;
        }
        return false;
    }
    function confirmDeleteInternalCommentJS(objid){
        var isDelete = confirm("Deleting this item is irreversible. Touch OK to continue or CANCEL to abort.");
        if (isDelete) {
            deleteInternalIdeaComment(objid);
            return true;
        }
        return false;
    }
    </script>
</apex:page>