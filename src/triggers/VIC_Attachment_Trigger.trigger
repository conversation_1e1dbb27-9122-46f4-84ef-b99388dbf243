/*
* refactoring done: STRY0450167, <PERSON><PERSON><PERSON>
*/
trigger VIC_Attachment_Trigger on VIC_Order__c (before Insert, before update, after update, after insert) {
    
    public static Boolean firstRun = false;
    List<Contact> conupdatelist = new List<Contact>();
    if(Trigger.isBefore) {
        Set<Id> setConIds = new Set<Id>();
        Map<Id,Id> mapConAccId = new Map<Id,Id>();
        Map<Id, VIC_Contracts__c> mapAccVicContract = new Map<Id, VIC_Contracts__c>(); 
        //STRY0450167 START common code for both before insert and before update
        for(VIC_Order__c vc: Trigger.New) {
            if(vc.Contact__c != null) {
                setConIds.add(vc.Contact__c);
            }
        }
        if(!setConIds.isEmpty()) {
            for(Contact con : [Select Id,Email,VIC_Registration__c, AccountId From Contact Where Id IN: setConIds]) {
                mapConAccId.put(con.Id, con.AccountId);
            }
            if(!mapConAccId.isEmpty()) {
                for(VIC_Contracts__c vicCon : [Select Id, Account__c, Cost_Center__c From VIC_Contracts__c Where Account__c IN: mapConAccId.values()]) {
                    mapAccVicContract.put(vicCon.Account__c, vicCon);
                }
            }
        }
        //STRY0450167 END
        if(Trigger.isInsert) {
            for(VIC_Order__c vc: Trigger.New) {
                vc.Status__c = 'In Progress';
                vc.Is_New_VM__c = true;
                try {
                    //Below Auth Class is not consistent. Sometimes, it throws exception, hence put in try-catch
                    if(!Test.isRunningTest() && (vc.Current_User_IP_Address__c == null || vc.Current_User_IP_Address__c == ''))
                        vc.Current_User_IP_Address__c = String.valueOf(Auth.SessionManagement.getCurrentSession().get('SourceIp'));
                } catch(Exception e) {} 
                if(vc.Contact__c != null && mapConAccId.containsKey(vc.Contact__c) && mapAccVicContract.containsKey(mapConAccId.get(vc.Contact__c))) {
                    vc.VIC_Cost_Center__c = mapAccVicContract.get(mapConAccId.get(vc.Contact__c)).Cost_Center__c;
                }
                vc.VIC_Cost_Center__c = String.isNotBlank(vc.VIC_Cost_Center__c) ? vc.VIC_Cost_Center__c : '0000000000';
            }
        }
        //STRY0450167 START
        if(Trigger.isUpdate) {
            for(VIC_Order__c vc: Trigger.New) {
                if(Trigger.OldMap.get(vc.Id).VM_Power_Off__c != vc.VM_Power_Off__c) {
                    vc.VM_Connect_Status__c = true;
                }
                if(vc.Contact__c != null && mapConAccId.containsKey(vc.Contact__c) && mapAccVicContract.containsKey(mapConAccId.get(vc.Contact__c))) {
                    vc.VIC_Cost_Center__c = mapAccVicContract.get(mapConAccId.get(vc.Contact__c)).Cost_Center__c;
                }
                vc.VIC_Cost_Center__c = String.isNotBlank(vc.VIC_Cost_Center__c) ? vc.VIC_Cost_Center__c : '0000000000';
                //STRY0237248 START
                if(Trigger.OldMap.get(vc.Id).Status__c != vc.Status__c && (vc.Status__c == 'Pending Deletion' || vc.Status__c == 'Trial Limit Exceeded')) {
                    vc.VM_Power_Off__c = null;
                }
                //STRY0237248 END
            }
        }
        //STRY0450167 END
    }
    
    if(Trigger.isAfter) {
        //STRY0450167 START removing SOQL from loop
        Set<Id> setConIds = new Set<Id>();
        Map<Id,Contact> mapContacts = new Map<Id,Contact>();
        for(VIC_Order__c vc: Trigger.New) {
            if(vc.Contact__c != null) {
                 setConIds.add(vc.Contact__c);
            }
        }
        if(!setConIds.isEmpty()) {
            mapContacts = new Map<Id,Contact>([Select Id,Email,VIC_Registration__c, AccountId From Contact Where Id IN: setConIds]);
        }
        //STRY0450167 END
        if(Trigger.isUpdate) {
        try{
            List<VIC_Order__c> vicOrdersHoursUpdateList = new List<VIC_Order__c>();
            List<Attachment> listAttachToInsert = new List<Attachment>();
            List<VIC_Order__c> lstVICOrderPowerOnVMs = new List<VIC_Order__c>();
            List<VIC_Order__c> lstVICOrderPowerOffVMs = new List<VIC_Order__c>();
            
            for(VIC_Order__c vc: Trigger.New) {
                String oldPowerStatus = Trigger.oldMap.get(vc.Id).VM_Power_Off__c;
                String newPowerStatus = vc.VM_Power_Off__c;
                if(vc.VM_Name__c != null && vc.VM_DNS_Name__c != null && vc.Status__c == 'Created' && Trigger.OldMap.get(vc.Id).Status__c != 'Created') {
                    String FileName = String.valueOf(vc.VM_Name__c) + '.rdp';
                    String FileContents = 'full address:s:' + vc.VM_DNS_Name__c + '\r\nusername:s:vic\\'+vc.VM_Username__c+'\r\nprompt for credentials:i:1\r\nadministrative session:i:1';
                    Blob blobContent = Blob.valueOf(FileContents);
                    Attachment attachment = new Attachment();
                    attachment.Body = blobContent;
                    attachment.Name = String.valueOf(FileName);
                    attachment.ParentId = vc.Id;
                    attachment.contentType = 'application/x-rdp';
                    listAttachToInsert.add(attachment);
                    //STRY0450167 START removing SOQL from loop
                    if(vc.Contact__c != null && mapContacts.containsKey(vc.Contact__c)) {
                        System.debug('=====Email==='+vc.Contact__r.Email);
                        System.debug('=====vc.Id==='+vc.Id);
                        VIC_CreateEnvironmentcont.sendEmail(mapContacts.get(vc.Contact__c).Email, vc.Id);
                        //STRY0450167 END
                    } else {
                        VIC_CreateEnvironmentcont.sendEmail(UserInfo.getUserEmail(), vc.Id);
                    }
                } else if(vc.Status__c == 'Trial Limit Exceeded' && Trigger.OldMap.get(vc.Id).Status__c != 'Trial Limit Exceeded'){
                    //STRY0181287 - added else if statement update VIC_Registration__c on contact if trial env is expired
                    if(vc.Contact__c != null) {
                        //STRY0450167 removing SOQL from loop
                        Contact conupdate = new Contact();
                        conupdate.VIC_Registration__c = true;
                        conupdate.Id = vc.Contact__c;
                        conupdatelist.add(conupdate);
                    }    
                }  
                
                //STRY0470379 - VM Power On Scenario
                if(oldPowerStatus != 'Powered On' && newPowerStatus == 'Powered On'){
                    lstVICOrderPowerOnVMs.add(vc);
                }
                //STRY0470379 - VM Power off scenario
                else if(oldPowerStatus == 'Powered On' && newPowerStatus != 'Powered On'){
                    lstVICOrderPowerOffVMs.add(vc);
                }
            }
             //STRY0470379- VM Consumed Hours Logic 
            if(!lstVICOrderPowerOnVMs.isEmpty() || !lstVICOrderPowerOffVMs.isEmpty()){
                VIC_Order.createUpdateVICConsumedHourRecord(lstVICOrderPowerOnVMs,lstVICOrderPowerOffVMs);
            }   
            if(!listAttachToInsert.isEmpty()) {
                insert listAttachToInsert;
            }
            if(!conupdatelist.isEmpty()){
                Database.update(conupdatelist,false);
            }
        } catch(Exception e){
            
        }
        }
    }
    //STRY0450167 START moved after insert logic of populating cost center to before insert
}