trigger updateopportunityFamily on OpportunityLineItem (after insert, after update) {    
    Set<Id> oppId = new Set<Id>();
    Map<Id,Opportunity> oppUpdateList = new Map<Id,Opportunity>();
    Set<Id> productIds = new Set<Id>();
    // STRY0160489 - Naren - insert rate card approval matrix record - Start
    list<VISRateCardApprovalMatrix__c> ratecardMatrix = new List<VISRateCardApprovalMatrix__c>();
    
    for(OpportunityLineItem oli : trigger.new){
        oppId.add(oli.OpportunityId);
    }
    
    list<Opportunity> oppp = [Select Id,RateCardApprovalStep__c,Deliver_to_Country__c,OppProductMaxDiscount__c,RecordTypeId,(Select Id,Product2Id,Discount from OpportunityLineItems Order by Discount Desc limit 1) 
    from opportunity Where id=:oppId AND RecordType.DeveloperName = 'VMS_Sales_IO_User'];
    
    for(Opportunity op : oppp){
        for(OpportunityLineItem oli : op.OpportunityLineItems){
            productIds.add(oli.Product2Id);
        }
    }
    if(!productIds.isEmpty()){
        ratecardMatrix = [Select Id,ProductName__c,MaximumDiscount__c,MinimumDiscount__c,Country__c,ApproverRoles__c from VISRateCardApprovalMatrix__c Where ProductName__c IN : productIds];
    }
    for(Opportunity op : oppp){            
        for(OpportunityLineItem oli : op.OpportunityLineItems){
            if(oli.Discount == 0){
                op.RateCardApprovalStep__c = 0;
                oppUpdateList.put(op.Id,op);
            }
            else{
                for(VISRateCardApprovalMatrix__c ram : ratecardMatrix ){
                    if(oli.Discount != null && op.Deliver_to_Country__c == ram.Country__c && oli.Product2Id == ram.ProductName__c && oli.Discount > ram.MinimumDiscount__c && oli.Discount <= ram.MaximumDiscount__c){
                        if(ram.ApproverRoles__c == 'Territory Manager'){
                            op.RateCardApprovalStep__c = 0;                             
                        } 
                        else if(ram.ApproverRoles__c == 'Regional Manager'){
                            op.RateCardApprovalStep__c = 1;
                        }
                        else if(ram.ApproverRoles__c == 'Regional Manager;Vice President Sales'){
                            op.RateCardApprovalStep__c = 2;
                        }
                        else if(ram.ApproverRoles__c == 'Regional Manager;Vice President Sales;President'){
                            op.RateCardApprovalStep__c = 3;
                        }
                        oppUpdateList.put(op.Id,op);
                        break;
                    }
                }
            }
        }
    }
    if(oppUpdateList != null){
        update oppUpdateList.values();
    }
    // STRY0160489 - Naren - insert rate card approval matrix record - End
}